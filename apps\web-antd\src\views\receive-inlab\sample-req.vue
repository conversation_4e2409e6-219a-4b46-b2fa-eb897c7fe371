<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';
import type { ReceiveInLabApi } from '#/api/receive-inlab/receive-inlab';

import { watch } from 'vue';

import { confirm, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  chkActiveNum,
  finishAliquot,
  getSamplingRequirements,
  removedSamplingRequirement,
} from '#/api/receive-inlab/receive-inlab';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddBatchRequirementForm from '../login-options/batch-manager/add-batch-requirement.vue';
import {
  useReceiveSampleRequirementColumns,
  useReceiveSampleRequirementFilterSchema,
} from './receive-inlab-data';
import Detail from './sample-req-test.vue';

const props = defineProps<{
  currentTestRow: null | ReceiveInLabApi.ReceiveOrders;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useReceiveSampleRequirementColumns();
const filterSchema = useReceiveSampleRequirementFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getSamplingRequirements(props.currentTestRow.ORDNO);
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi } =
  useLimsGridsConfig<BatcheManagerApi.BatchSamplingRequirement>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function onRefresh() {
  gridApi.query();
}

// 添加取样要求
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddBatchRequirementForm,
});

async function onCreate() {
  if (props.currentTestRow === null) return;
  const batchid = props.currentTestRow.ORDNO;
  const sampleGroupCode = null;
  formModalApi
    .setData({ BATCHID: batchid, SAMPLEGROUPCODE: sampleGroupCode })
    .open();
}

// 移除取样要求
async function onRemove() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请先选择要移除的数据');
    return;
  }
  if (props.currentTestRow === null) return;
  const sOrdNo = props.currentTestRow.ORDNO;
  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const bRet = await removedSamplingRequirement(aOrigrec, '', sOrdNo);
    if (!bRet) {
      message.warn($t('notify-samplers.noSamplingRequirement'));
      return;
    }

    message.success('移除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 完成分样
async function onFinishAliquots() {
  if (props.currentTestRow === null) return;
  const sOrdNo = props.currentTestRow.ORDNO;
  try {
    await confirm({
      title: '确认完成',
      content: `确定要完成分样吗？`,
      icon: 'warning',
      centered: false,
    });

    const ret = await chkActiveNum(sOrdNo);
    if (!ret) {
      message.warn($t('receive-inlab.ActiveNumIsNotNull'));
      return;
    }

    await finishAliquot(sOrdNo);

    message.success('操作成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

async function onAliquots() {
  // 获取选中行
  // const sampleReq = gridApi.grid?.getCurrentRecord();
  // if (!sampleReq) return;
  // if (props.currentTestRow === null) return;
  // const xOrdNo = props.currentTestRow.XORDNO;
  // if (xOrdNo > 0) {
  //   await aliquots(xOrdNo);
  // }
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Detail,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onDetail(row: BatcheManagerApi.BatchSamplingRequirement) {
  formDrawerApi.setData(row).open();
}
</script>

<template>
  <FormDrawer @success="onRefresh" />
  <FormModal @success="onRefresh" />
  <div class="h-[350px] w-full">
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onRemove">
            {{ $t('login-options.remove') }}
          </Button>
          <Button type="default" @click="onAliquots">
            {{ $t('receive-inlab.aliquots') }}
          </Button>
          <Button type="default" @click="onFinishAliquots">
            {{ $t('receive-inlab.finishAliquots') }}
          </Button>
          <Button type="default">
            {{ $t('login-options.printReqLabel') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Button type="link" @click="onDetail(row)">
          {{ $t('login-options.batchManager.reqTests') }}
        </Button>
      </template>
    </Grid>
  </div>
</template>
