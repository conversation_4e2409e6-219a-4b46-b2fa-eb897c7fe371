import { backendRequestClient, baseRequestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    userId: string;
    accessToken: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  // return requestClient.post<AuthApi.LoginResult>('/auth/login', data);
  return backendRequestClient.post<AuthApi.LoginResult>('/auth/login', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return backendRequestClient.post<AuthApi.RefreshTokenResult>(
    '/auth/refresh-token',
    {},
    {
      withCredentials: true,
    },
  );
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi(userId: string) {
  const userPermissions = await backendRequestClient.get(
    `/platform/users/${userId}/permissions`,
  );
  return userPermissions
    .filter((permission: { code: string; isEnabled: boolean }) => {
      return permission.isEnabled;
    })
    .map((permission: { code: string }) => permission.code);
}
