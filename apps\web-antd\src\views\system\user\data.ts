import type { Ref } from 'vue';

import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemUserApi } from '#/api';

import dayjs from 'dayjs';

import { z } from '#/adapter/form';
import { getDeptsForSelect, getRolesForSelect } from '#/api';
import { $t } from '#/locales';

/**
 * 定义抽屉表单 Schema
 */
export function useFormSchema(
  formData: Ref<SystemUserApi.User>,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userName',
      label: $t('system.user.userName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'realName',
      label: $t('system.user.fullName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'password',
      label: $t('system.user.password'),
      rules: 'required',
      dependencies: {
        if: (_values, _formApi) => {
          return !formData.value?.userId;
        },
        triggerFields: ['userId'],
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: $t('system.user.sexMale'), value: 0 }, // 0 代表男
          { label: $t('system.user.sexFemale'), value: 1 }, // 1 代表女
          { label: $t('system.user.sexUnknown'), value: 2 }, // 2 代表未知
        ],
      },
      fieldName: 'sexValue',
      label: $t('system.user.sex'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: $t('system.user.email'),
      rules: z.string().email($t('system.user.emailFormatError')),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        mode: 'multiple',
        api: getDeptsForSelect,
        labelField: 'name',
        valueField: 'code',
        class: 'w-full',
      },
      fieldName: 'depts',
      label: $t('system.user.depts'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        mode: 'multiple',
        api: getRolesForSelect,
        labelField: 'name',
        valueField: 'code',
        class: 'w-full',
      },
      fieldName: 'roles',
      label: $t('system.user.roles'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'number',
      },
      fieldName: 'phone',
      label: $t('system.user.phone'),
    },
    {
      component: 'Input',
      fieldName: 'address',
      label: $t('system.user.address'),
    },
    {
      component: 'Upload',
      componentProps: {
        accept: '.png,.jpg,.jpeg',
        disabled: false,
        maxCount: 1,
        multiple: false,
        showUploadList: true,
        listType: 'picture-card',
        beforeUpload: (file: File) => {
          return new Promise((resolve) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.addEventListener('load', () => {
              resolve(false); // 阻止默认上传
            });
          });
        },
        // onChange: (info: { file: File }) => {
        //   const reader = new FileReader();
        //   reader.readAsDataURL(info.file);
        //   reader.addEventListener('load', () => {
        //     const base64 = reader.result as string;
        //     // 将base64赋值给表单字段
        //     formData.value.icon = base64;
        //   });
        // },
      },
      fieldName: 'icon',
      label: $t('system.user.icon'),
      renderComponentContent: () => {
        return {
          default: () => $t('system.user.uploadImage'),
        };
      },
      // rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'stateValue',
      label: $t('system.user.state'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: $t('commons.remark'),
    },
  ];
}

/**
 * 定义搜索表单 Schema
 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'keyword',
      label: $t('system.user.searchPlaceholder'), // 用户名/姓名/邮箱/手机号
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'stateValue',
      label: $t('system.user.state'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('commons.createdOn'),
    },
  ];
}

/**
 * 定义表格列
 * @param onActionClick 操作列按钮点击事件
 * @param onStatusChange 状态开关改变事件
 */
export function useColumns(
  onActionClick: OnActionClickFn<any>,
  onStatusChange?: (newStatus: any, row: any) => Promise<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'userId',
      title: $t('commons.id'),
      width: 100,
      visible: false,
    },
    {
      field: 'userName',
      title: $t('system.user.userName'),
      minWidth: 120,
    },
    {
      field: 'realName',
      title: $t('system.user.fullName'),
      minWidth: 120,
    },
    {
      field: 'depts',
      title: $t('system.user.depts'),
      minWidth: 120,
      cellRender: {
        name: 'CellTags',
      },
    },
    {
      field: 'roles',
      title: $t('system.user.roles'),
      minWidth: 120,
      cellRender: {
        name: 'CellTags',
      },
    },
    {
      field: 'sexValue',
      title: $t('system.user.sex'),
      width: 80,
      formatter: ({ cellValue }) => {
        const sexList: { label: string; value: number }[] = [
          { label: $t('system.user.sexMale'), value: 0 },
          { label: $t('system.user.sexFemale'), value: 1 },
          { label: $t('system.user.sexUnknown'), value: 2 },
        ];
        const item = sexList.find((item) => item.value === cellValue);
        return item ? item.label : cellValue;
      },
    },
    {
      field: 'email',
      title: $t('system.user.email'),
      minWidth: 150,
    },
    {
      field: 'phone',
      title: $t('system.user.phone'),
      minWidth: 120,
    },
    {
      field: 'address',
      title: $t('system.user.address'),
      minWidth: 150,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'stateValue',
      title: $t('system.user.state'),
      width: 100,
    },
    {
      field: 'remark',
      title: $t('commons.remark'),
      minWidth: 150,
    },
    {
      field: 'createdOnUtc',
      title: $t('commons.createdOn'),
      width: 150,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      field: 'lastModifiedOnUtc',
      title: $t('commons.createdOn'), // 使用已有的翻译，实际显示为"创建时间"
      width: 150,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss');
      },
      visible: false,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'userName',
          nameTitle: $t('system.user.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('commons.action'),
      width: 130,
    },
  ];
}
