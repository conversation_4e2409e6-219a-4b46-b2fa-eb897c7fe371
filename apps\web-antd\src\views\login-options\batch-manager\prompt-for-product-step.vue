<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { computed, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import AdHocForm from './adhoc-tp.vue';
import InspectionForm from './inspection-tp.vue';
import rawMaterialForm from './raw-material-tp.vue';

// const emit = defineEmits(['success']);

// 为每个表单创建独立的ref
const rawMaterialFormRef = ref();
const adHocFormRef = ref();
const inspectionFormRef = ref();

const activeKey = ref('1');

// 表单Key与表单实例的映射关系
const FORM_KEY_MAP = {
  '1': 'rawMaterial', // 原辅包
  '2': 'adHoc', // 常规样
  '3': 'inspection', // 免检
} as const;

// 表单实例映射
const formInstanceMap = computed(() => ({
  rawMaterial: rawMaterialFormRef.value,
  adHoc: adHocFormRef.value,
  inspection: inspectionFormRef.value,
}));

// 表单API映射 - 用于获取表单的API实例
const getFormApi = (formInstance: any) => {
  if (!formInstance) return null;

  // 根据不同表单组件的暴露方式获取API
  if (formInstance.rawMaterialFormApi) {
    return formInstance.rawMaterialFormApi;
  }
  if (formInstance.adHocFormApi) {
    return formInstance.adHocFormApi;
  }
  if (formInstance.inspectionFormApi) {
    return formInstance.inspectionFormApi;
  }
  return null;
};

/**
 * 根据Key调用对应表单的validate方法
 * @param key 表单标识符 ('1': 原辅包, '2': 常规样, '3': 免检)
 * @returns Promise<{success: boolean, data?: any, error?: string, validationResult?: any}>
 */
const validateFormByKey = async (key: string) => {
  try {
    // 验证Key是否有效
    if (!FORM_KEY_MAP[key as keyof typeof FORM_KEY_MAP]) {
      const error = `无效的表单Key: ${key}。有效的Key为: ${Object.keys(FORM_KEY_MAP).join(', ')}`;
      console.error(error);
      return { success: false, error };
    }

    // 获取表单类型
    const formType = FORM_KEY_MAP[key as keyof typeof FORM_KEY_MAP];

    // 获取表单实例
    const formInstance = formInstanceMap.value[formType];
    if (!formInstance) {
      const error = `表单实例未找到: ${formType} (Key: ${key})`;
      console.error(error);
      return { success: false, error };
    }

    // 获取表单API
    const formApi = getFormApi(formInstance);
    if (!formApi) {
      const error = `表单API未找到: ${formType} (Key: ${key})`;
      console.error(error);
      return { success: false, error };
    }

    // 执行验证
    console.log(`开始验证表单: ${formType} (Key: ${key})`);

    const result = await formApi.validate();

    console.log(`表单验证完成: ${formType}`, result);

    // 检查验证结果中的 valid 字段
    if (result.valid === false) {
      // 构建错误信息
      const errorMessages = [];
      if (result.errors && typeof result.errors === 'object') {
        for (const [field, error] of Object.entries(result.errors)) {
          errorMessages.push(`${field}: ${error}`);
        }
      }

      const errorMsg =
        errorMessages.length > 0
          ? `表单验证失败：\n${errorMessages.join('\n')}`
          : '表单验证失败，请检查必填字段';

      console.error(`表单 ${formType} 验证失败:`, result.errors);
      return { success: false, error: errorMsg, validationResult: result };
    }

    // 验证成功
    return { success: true, data: result };
  } catch (error) {
    const errorMsg = `表单验证失败 (Key: ${key}): ${(error as Error).message}`;
    console.error(errorMsg, error);
    return { success: false, error: errorMsg };
  }
};

/**
 * 验证当前激活的表单
 * @returns Promise<{success: boolean, data?: any, error?: string, validationResult?: any}>
 */
const validateCurrentForm = async () => {
  return await validateFormByKey(activeKey.value);
};

/**
 * 验证所有表单
 * @returns Promise<{success: boolean, results: Array<{key: string, success: boolean, data?: any, error?: string}>}>
 */
const validateAllForms = async () => {
  const results = [];
  let allSuccess = true;

  for (const key of Object.keys(FORM_KEY_MAP)) {
    const result = await validateFormByKey(key);
    results.push({ key, ...result });
    if (!result.success) {
      allSuccess = false;
    }
  }

  return { success: allSuccess, results };
};

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: false,
  onConfirm: async () => {
    // 验证当前激活的表单
    const result = await validateCurrentForm();

    if (result.success) {
      // 验证成功，继续执行后续流程
      message.success('表单验证成功');
      // emit('success');
      modalApi.close();
    } else {
      // 验证失败，显示错误信息并阻止Modal关闭
      console.error('表单验证失败:', result.error);

      // 显示用户友好的错误提示
      message.error({
        content: result.error || '表单验证失败，请检查必填字段',
        duration: 5, // 显示5秒
      });

      // 不关闭Modal，让用户有机会修正表单数据
      // modalApi.close(); // 注释掉这行，阻止Modal关闭

      // 可选：滚动到第一个错误字段
      if (result.validationResult?.errors) {
        const firstErrorField = Object.keys(result.validationResult.errors)[0];
        console.warn(`请检查字段: ${firstErrorField}`);
      }
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<BatcheManagerApi.Batches>();
      if (data) {
        // formData.value = data;
      }
    }
  },
});

// 暴露方法供外部调用
defineExpose({
  validateFormByKey,
  validateCurrentForm,
  validateAllForms,
  FORM_KEY_MAP,
});
</script>
<template>
  <Modal title="getTitle" class="h-[1000px] w-[1200px]">
    <Page auto-content-height>
      <Space align="center" style="width: 100%" class="justify-center">
        <Button type="primary"> 原辅包 </Button>
        <Button type="primary"> 常规样 </Button>
        <Button type="primary"> 免检 </Button>
      </Space>
      <Tabs v-model:active-key="activeKey">
        <TabPane key="1" tab="原辅包">
          <rawMaterialForm ref="rawMaterialFormRef" />
        </TabPane>
        <TabPane key="2" tab="常规样">
          <AdHocForm ref="adHocFormRef" />
        </TabPane>
        <TabPane key="3" tab="免检">
          <InspectionForm ref="inspectionFormRef" />
        </TabPane>
      </Tabs>
    </Page>
  </Modal>
</template>
