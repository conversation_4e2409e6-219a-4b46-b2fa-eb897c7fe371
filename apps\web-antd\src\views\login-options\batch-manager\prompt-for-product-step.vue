<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

// 添加了 onMounted 和 reactive 的导入
import { Page, useVbenModal } from '@vben/common-ui';

import { Button, TabPane, Tabs } from 'ant-design-vue';

import AdHocForm from './adhoc-tp.vue';
import InspectionForm from './inspection-tp.vue';
// import AddForm from './raw-material-tp.vue';
// import AddForm from './adhoc-tp.vue';
import rawMaterialForm from './raw-material-tp.vue';

// const emit = defineEmits(['success']);

const formRef = ref();

const activeKey = ref('1');

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: false,
  onConfirm: async () => {
    const st = await formRef.value?.validate();
    console.log(st);
    // emit('success');
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<BatcheManagerApi.Batches>();
      if (data) {
        // formData.value = data;
      }
    }
  },
});
</script>
<template>
  <Modal title="getTitle" class="h-[1000px] w-[1200px]">
    <Page auto-content-height>
      <Button type="primary"> 原辅包 </Button>
      <Button type="primary"> 常规样 </Button>
      <Button type="primary"> 免检 </Button>
      <Tabs v-model:active-key="activeKey">
        <TabPane key="1" tab="原辅包">
          <rawMaterialForm ref="formRef" />
        </TabPane>
        <TabPane key="2" tab="常规样">
          <AdHocForm />
        </TabPane>
        <TabPane key="3" tab="免检">
          <InspectionForm />
        </TabPane>
      </Tabs>
    </Page>
  </Modal>
</template>
