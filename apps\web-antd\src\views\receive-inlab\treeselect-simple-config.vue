<!-- 简化版 TreeSelect 配置示例 -->
<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { message } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { getLocationList } from '#/api/materials-management/location-management';
import { $t } from '#/locales';

const emit = defineEmits(['success']);
const formData = ref();

// 简化版：直接使用 ApiTreeSelect 的内置功能
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  layout: 'vertical',
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'COMPDAT',
      label: $t('receive-inlab.compdat'),
      rules: 'required',
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'LOCATIONCODE',
      label: $t('receive-inlab.locationcode'),
      rules: 'required',
      componentProps: {
        // 基本配置
        allowClear: true,
        placeholder: '请选择位置',
        showSearch: true,
        treeDefaultExpandAll: false,
        
        // API配置 - 最简单的方式
        api: () => getLocationList('SITE1'),
        immediate: true,
        
        // 字段映射 - 关键配置
        labelField: 'TEXTMEMBER',
        valueField: 'VALUEMEMBER',
        childrenField: 'children',
        
        // 数据转换 - 将扁平数据转为树形结构
        afterFetch: (data: any[]) => {
          if (!data || !Array.isArray(data)) return [];
          
          // 简单的树形转换
          const map = new Map();
          const roots: any[] = [];
          
          // 创建节点映射
          data.forEach(item => {
            map.set(item.VALUEMEMBER, {
              ...item,
              children: [],
            });
          });
          
          // 建立父子关系
          data.forEach(item => {
            const node = map.get(item.VALUEMEMBER);
            if (item.PARENTMEMBER && map.has(item.PARENTMEMBER)) {
              const parent = map.get(item.PARENTMEMBER);
              parent.children.push(node);
            } else {
              roots.push(node);
            }
          });
          
          return roots;
        },
        
        // 搜索配置
        treeNodeFilterProp: 'TEXTMEMBER',
        
        // 事件处理
        onSelect: (value: string, node: any) => {
          console.log('选中位置:', { value, node });
        },
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formData.value = modalApi.getData<Record<string, any>>();
    }
  },
  title: '接收样品',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = await formApi.getValues();
    console.log('提交数据:', data);

    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>

<!-- 
使用说明：

1. 基本配置
   - component: 'ApiTreeSelect' - 使用API树选择组件
   - api: () => getLocationList('SITE1') - API调用函数
   - immediate: true - 立即加载数据

2. 字段映射
   - labelField: 'TEXTMEMBER' - 显示文本字段
   - valueField: 'VALUEMEMBER' - 选中值字段
   - childrenField: 'children' - 子节点字段

3. 数据转换
   - afterFetch: 数据转换函数，将扁平数据转为树形结构
   - 自动处理父子关系建立

4. 搜索功能
   - showSearch: true - 启用搜索
   - treeNodeFilterProp: 'TEXTMEMBER' - 搜索字段

5. 事件处理
   - onSelect: 选择节点时的回调函数

这种配置方式更简单，适合大多数使用场景。
-->
