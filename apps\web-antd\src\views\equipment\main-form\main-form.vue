<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { MainFormApi } from '#/api/equipment/main-form';

import { computed, ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  checkFinishApi,
  getBlanceListApi,
  getDailyCailbApi,
} from '#/api/equipment/main-form';
import { useMainFormStore } from '#/store';

import CommonModel from './components/common-modal.vue';
import Files from './components/files.vue';
import ValidationRecord from './components/validation-record.vue';
import { balanceColumns, recordColumns } from './main-form-data';

interface RowType {
  EQID?: string;
  [key: string]: any;
}
const mainFormStore = useMainFormStore();
const currentRow = computed<RowType>(
  () => mainFormStore.getCurrentRow as unknown as RowType,
);

watch(currentRow, (newRow) => {
  if (newRow) {
    eventGridApi.query();
  }
});
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: CommonModel,
  destroyOnClose: true,
});
// const [SearchFormModal, searchModalApi] = useVbenModal({
//   connectedComponent: SearchModal,
//   destroyOnClose: true,
// });
const gridEvents: VxeGridListeners<MainFormApi.MainForm> = {
  currentChange: async ({ row }) => {
    if (row) {
      mainFormStore.setCurrentRow(row);
    }
  },
};
const eventGridEvents: VxeGridListeners<MainFormApi.MainForm> = {
  currentChange: async ({ row }) => {
    if (row) {
      mainFormStore.setEventRow(row);
    }
  },
};
const gridOptions: VxeTableGridOptions<MainFormApi.MainForm> = {
  columns: balanceColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = await getBlanceListApi();
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const eventgridOptions: VxeTableGridOptions<MainFormApi.MainForm> = {
  columns: recordColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const data = await getDailyCailbApi([currentRow.value?.EQID]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const [Grid] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
  tableTitle: '天平列表',
});
const [EventGrid, eventGridApi] = useVbenVxeGrid({
  gridOptions: eventgridOptions,
  gridEvents: eventGridEvents,
  tableTitle: '事件记录',
});
const tabList = [
  {
    title: '校验记录 ',
    page: ValidationRecord,
  },
  {
    title: '附件',
    page: Files,
  },
];
const activeKey = ref('校验记录');
const onRefresh = () => {
  eventGridApi.query();
};
const initiateCalibration = () => {
  formModalApi.setData(null).open();
};
const completeCalibration = async () => {
  const row = mainFormStore.getCurrentRow as unknown as RowType;
  if (!row) {
    message.warning('请选择一条记录');
    return;
  }
  const params = [row.ORIGREC, row.MAINTENANCEEVENT];
  const data = await checkFinishApi(params);
  const messageObj = {
    '-1': '请为日校事件至少设置三个校验点！',
    '-2': '请为日校事件保证一个有且仅有一个目标点！',
    '-3': '请为日校事件至少添加一个砝码值比目标点高的校验点！',
    '-4': '请为日校事件至少添加一个砝码值比目标点低的校验点！',
    '-5': '请为内部校准至少添加一个校准点！',
    '-6': '一个或多个校准点没有结论',
  };
  message.warning(messageObj[data as keyof typeof messageObj]);
  // TODO电子签名校验
};

const addAttachment = () => {
  // TODO:上传文件
};
const viewAttachment = () => {
  // TODO:下载文件
};
const deleteAttachment = () => {
  // TODO:删除文件
};
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />

    <div class="flex h-full w-full flex-row">
      <Grid class="w-2/5" />
      <div class="h-full flex-1 bg-white">
        <EventGrid class="h-1/3 w-full">
          <template #toolbar-actions>
            <Space :size="[8, 0]" wrap>
              <Button type="primary" @click="initiateCalibration">
                {{ $t('equipment.main-form.initiateCalibration') }}
              </Button>
              <Button type="primary" @click="completeCalibration">
                {{ $t('equipment.main-form.completeCalibration') }}
              </Button>
              <Button type="primary" @click="addAttachment">
                {{ $t('equipment.main-form.addAttachment') }}
              </Button>
              <Button type="primary" @click="viewAttachment">
                {{ $t('equipment.main-form.viewAttachment') }}
              </Button>
              <Button type="primary" @click="deleteAttachment">
                {{ $t('equipment.main-form.deleteAttachment') }}
              </Button>
            </Space>
          </template>
        </EventGrid>
        <Tabs v-model:active-key="activeKey" class="w-full">
          <TabPane
            v-for="item in tabList"
            :key="item.title"
            :tab="item.title"
          />
        </Tabs>
        <div class="h-3/5">
          <component
            :is="
              tabList.find((item) => item.title === activeKey)?.page ||
              ValidationRecord
            "
          />
        </div>
      </div>
    </div>
  </Page>
</template>
