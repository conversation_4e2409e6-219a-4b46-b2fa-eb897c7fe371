---
description:
globs:
alwaysApply: false
---
# 项目结构指南

这是一个基于 monorepo 架构的项目，主要包含以下目录结构：

- `apps/`: 包含所有应用程序
- `packages/`: 包含共享的库和组件
- `docs/`: 项目文档
- `scripts/`: 构建和开发脚本
- `internal/`: 内部工具和配置

## 主要配置文件

- [package.json](mdc:package.json): 项目的主要配置文件
- [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml): 工作空间配置
- [turbo.json](mdc:turbo.json): Turborepo 配置
- [.npmrc](mdc:.npmrc): NPM 配置

## 代码规范配置

- [.prettierrc.mjs](mdc:.prettierrc.mjs): 代码格式化配置
- [.eslintrc.js](mdc:.eslintrc.js): ESLint 配置
- [.stylelintrc.js](mdc:.stylelintrc.js): StyleLint 配置
- [.commitlintrc.js](mdc:.commitlintrc.js): 提交信息规范配置

## 开发工具配置

- [.vscode/](mdc:.vscode/): VS Code 配置
- [.github/](mdc:.github/): GitHub 工作流配置
- [.husky/](mdc:.husky/): Git hooks 配置
