# 表单提交卡死问题修复说明

## 🔍 问题分析

### 根本原因
页面卡死的根本原因是在 `raw-material-tp.vue` 中的 `onRawMaterialSubmit` 方法存在**无限递归调用**：

```typescript
// ❌ 错误的实现（导致无限递归）
async function onRawMaterialSubmit(values: Record<string, any>) {
  await rawMaterialFormApi.validateAndSubmitForm(); // 这里会再次调用 onRawMaterialSubmit
  // ... 其他代码
}
```

### 调用链分析
1. `submitFormByKey` 调用 `onRawMaterialSubmit(formValues)`
2. `onRawMaterialSubmit` 调用 `rawMaterialFormApi.validateAndSubmitForm()`
3. `validateAndSubmitForm()` 内部又调用 `onRawMaterialSubmit`
4. 形成无限循环 → 页面卡死

## ✅ 修复方案

### 1. 修复子组件提交方法

#### raw-material-tp.vue
```typescript
// ✅ 修复后的实现
async function onRawMaterialSubmit(values: Record<string, any>) {
  try {
    console.log('开始提交原辅包表单数据:', values);
    
    const data = values as BatcheManagerApi.Batches;
    data.SAMPLEGROUPCODE = sSampleGroupCode.value;
    data.STORAGE_CONDITION = sRawStorageCondition.value;
    
    await addRawMatSample(data);
    emit('success');
    
    console.log('原辅包表单提交成功');
    return { success: true, data };
  } catch (error) {
    console.error('原辅包表单提交失败:', error);
    throw error; // 重新抛出错误，让调用方处理
  }
}
```

#### adhoc-tp.vue
```typescript
// ✅ 修复后的实现
async function onAdHocSubmit(values: Record<string, any>) {
  try {
    console.log('开始提交常规样表单数据:', values);
    
    const data = values as BatcheManagerApi.Batches;
    data.SKIPSAMP = data.SKIPSAMP ? 'Y' : 'N';
    data.SKIPREC = data.SKIPREC ? 'Y' : 'N';
    
    await addAdHocSample(data);
    emit('success');
    
    console.log('常规样表单提交成功');
    return { success: true, data };
  } catch (error) {
    console.error('常规样表单提交失败:', error);
    throw error;
  }
}
```

#### inspection-tp.vue
```typescript
// ✅ 修复后的实现
async function onInspectionSubmit(values: Record<string, any>) {
  try {
    console.log('开始提交免检表单数据:', values);
    
    const data = values as BatcheManagerApi.Batches;
    await addInspection(data);
    emit('success');
    
    console.log('免检表单提交成功');
    return { success: true, data };
  } catch (error) {
    console.error('免检表单提交失败:', error);
    throw error;
  }
}
```

### 2. 增强主文件的错误处理

#### 添加超时机制
```typescript
const withTimeout = (promise: Promise<any>, timeout: number, errorMessage: string): Promise<any> => {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => reject(new Error(errorMessage)), timeout);
    })
  ]);
};
```

#### 改进 submitFormByKey 方法
```typescript
const submitFormByKey = async (key: string) => {
  try {
    // ... 验证逻辑 ...
    
    // 执行提交，添加30秒超时
    const submitPromise = submitMethod(formValues);
    const result = await withTimeout(
      submitPromise,
      30000, // 30秒超时
      `表单提交超时: ${formType} (Key: ${key})`
    );
    
    return { success: true, data: result || formValues };
  } catch (error) {
    const errorMsg = `表单提交失败 (Key: ${key}): ${(error as Error).message}`;
    console.error(errorMsg, error);
    return { success: false, error: errorMsg };
  }
};
```

## 🧪 测试步骤

### 1. 测试修复效果
1. 打开表单页面
2. 填写必要的表单数据
3. 点击确认按钮
4. **期望结果**：
   - 不再出现页面卡死
   - 能看到控制台输出提交日志
   - 提交成功或失败都有明确反馈

### 2. 测试超时机制
1. 在网络较慢的环境下测试
2. 或者临时修改超时时间为较短值（如5秒）
3. **期望结果**：
   - 超时后显示超时错误信息
   - 页面不会卡死

### 3. 测试错误处理
1. 故意填写无效数据（如果API有验证）
2. 或者临时断网测试
3. **期望结果**：
   - 显示具体的错误信息
   - Modal不关闭，用户可以修正数据

## 🔧 关键修复点

1. **移除递归调用**：子组件提交方法不再调用 `validateAndSubmitForm()`
2. **简化提交逻辑**：直接调用API，处理成功/失败状态
3. **统一错误处理**：通过 throw error 让父组件统一处理
4. **添加超时保护**：防止网络问题导致的长时间等待
5. **改进日志输出**：便于调试和问题定位

## 🎯 修复效果

- ✅ 解决页面卡死问题
- ✅ 提供清晰的错误信息
- ✅ 添加超时保护机制
- ✅ 统一的错误处理流程
- ✅ 更好的调试支持

这个修复确保了表单提交功能的稳定性和可靠性，同时提供了更好的用户体验。
