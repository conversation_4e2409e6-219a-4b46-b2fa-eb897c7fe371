---
description: 
globs: 
alwaysApply: false
---
# Vue Vben Admin 组件开发规范

## UI 组件体系

### 核心 UI 组件 (`@core/ui-kit/`)

1. 布局组件 (`layout-ui/`)
- 整体布局
- 页面容器
- 响应式处理

2. 菜单组件 (`menu-ui/`)
- 主菜单
- 子菜单
- 上下文菜单

3. 表单组件 (`form-ui/`)
- 表单渲染
- 表单上下文
- 表单验证

4. 标签页组件 (`tabs-ui/`)
- 标签页管理
- 拖拽排序
- 滚动处理

5. 弹窗组件 (`popup-ui/`)
- 模态框
- 抽屉
- 消息提示

## 组件结构规范

### 标准组件结构
```bash
component-ui/
├── src/
│   ├── components/        # 子组件
│   │   ├── base/         # 基础子组件
│   │   └── functional/   # 功能子组件
│   ├── hooks/            # 组件钩子
│   │   ├── useContext.ts # 上下文钩子
│   │   └── useState.ts   # 状态钩子
│   ├── utils/           # 工具函数
│   ├── types.ts         # 类型定义
│   ├── config.ts        # 配置项
│   ├── index.ts         # 入口文件
│   └── vben-{name}.vue  # 主组件文件
├── package.json         # 包配置
├── tsconfig.json       # TS配置
└── build.config.ts     # 构建配置
```

### 组件命名规范

1. 文件命名
```bash
# 主组件
vben-layout.vue
vben-form.vue
vben-menu.vue

# 子组件
sub-menu.vue
form-item.vue

# 钩子文件
use-form-context.ts
use-tabs-drag.ts

# 类型文件
types.ts
```

2. 组件注册
```ts
// index.ts
export { default as VbenForm } from './vben-form.vue'
export { default as VbenFormItem } from './components/form-item.vue'
export * from './types'
```

## 组件开发规范

### 1. 类型定义 (`types.ts`)
```ts
// 组件 Props 类型
export interface VbenFormProps {
  schemas: FormSchema[]
  showActionButtonGroup?: boolean
  actionColOptions?: Partial<ColEx>
  submitOnReset?: boolean
  submitOnChange?: boolean
  size?: Size
  disabled?: boolean
}

// 组件事件类型
export interface VbenFormEmits {
  (e: 'register', instance: FormActionType): void
  (e: 'submit', values: any): void
  (e: 'reset', values: any): void
}

// 组件实例类型
export type FormInstance = InstanceType<typeof VbenForm>

// 组件方法类型
export interface FormActionType {
  submit: () => Promise<void>
  setProps: (props: Partial<VbenFormProps>) => void
  setValues: (values: Record<string, any>) => void
  getFieldsValue: () => Record<string, any>
}
```

### 2. 组件实现
```vue
<script setup lang="ts" generic="T extends VbenFormProps">
import { useForm } from './hooks/useForm'
import type { VbenFormEmits } from './types'

defineOptions({
  name: 'VbenForm',
})

const props = withDefaults(defineProps<T>(), {
  showActionButtonGroup: true,
  submitOnReset: false,
  submitOnChange: false,
  size: 'default',
})

const emit = defineEmits<VbenFormEmits>()

const { formRef, methods, state } = useForm(props, emit)
</script>

<template>
  <Form ref="formRef" v-bind="getBindValue" :class="getFormClass">
    <FormItems />
    <FormAction v-if="showActionButtonGroup" />
  </Form>
</template>
```

### 3. 组件钩子
```ts
// hooks/useForm.ts
export function useForm(
  props: VbenFormProps,
  emit: VbenFormEmits,
) {
  const formRef = ref<FormInstance | null>(null)
  
  const methods = {
    submit: async () => {
      const values = await formRef.value?.submit()
      emit('submit', values)
    },
    reset: () => {
      formRef.value?.reset()
      emit('reset')
    },
  }
  
  const state = reactive({
    // 组件状态
  })
  
  return {
    formRef,
    methods,
    state,
  }
}
```

### 4. 配置处理
```ts
// config.ts
import type { VbenFormProps } from './types'

export const defaultProps: VbenFormProps = {
  schemas: [],
  showActionButtonGroup: true,
  size: 'default',
}

export const getFormConfig = (props: VbenFormProps) => {
  return {
    ...defaultProps,
    ...props,
  }
}
```

## 组件测试规范

### 单元测试
```ts
import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import { VbenForm } from '../src'

describe('VbenForm', () => {
  it('should render correctly', () => {
    const wrapper = mount(VbenForm, {
      props: {
        schemas: [],
      },
    })
    expect(wrapper.exists()).toBe(true)
  })
  
  it('should emit submit event', async () => {
    const wrapper = mount(VbenForm)
    await wrapper.vm.methods.submit()
    expect(wrapper.emitted('submit')).toBeTruthy()
  })
})
```

## 组件文档规范

### README.md
```markdown
# @vben/form-ui

表单组件，用于数据录入、校验和提交。

## 安装

```bash
pnpm add @vben/form-ui
```

## 使用

```vue
<script setup lang="ts">
import { VbenForm } from '@vben/form-ui'
import type { FormSchema } from '@vben/form-ui'

const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '姓名',
  },
]
</script>

<template>
  <VbenForm :schemas="schemas" @submit="handleSubmit" />
</template>
```

## API

### Props

| 名称 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| schemas | FormSchema[] | [] | 表单配置项 |
| size | 'small' \| 'default' \| 'large' | 'default' | 表单尺寸 |

### Events

| 名称 | 参数 | 说明 |
|------|------|------|
| submit | (values: object) => void | 表单提交时触发 |
| reset | () => void | 表单重置时触发 |

### Methods

| 名称 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| submit | - | Promise<object> | 提交表单 |
| reset | - | void | 重置表单 |
```

### 示例代码
```bash
form-ui/
└── demo/
    ├── basic.vue        # 基础用法
    ├── advance.vue      # 高级用法
    ├── validate.vue     # 表单验证
    └── custom.vue       # 自定义组件
```

## 测试规范

### 单元测试
```ts
// ComponentName.test.ts
import { mount } from '@vue/test-utils'
import ComponentName from './ComponentName.vue'

describe('ComponentName', () => {
  test('renders properly', () => {
    const wrapper = mount(ComponentName)
    expect(wrapper.exists()).toBe(true)
  })
})
```

### E2E 测试
```ts
// ComponentName.spec.ts
import { test, expect } from '@playwright/test'

test('component interaction', async ({ page }) => {
  await page.goto('/demo/component')
  await expect(page.getByRole('button')).toBeVisible()
})

