<script lang="ts" setup>
import type { SizeType } from 'ant-design-vue/es/config-provider';

import type { SpecSchemasApi } from '#/api/business-static-tables';

import { ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, Col, message, Row, Space } from 'ant-design-vue';

import {
  $deleteSchemaNameApi,
  $getSpecSchemasApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddSpecSchemaForm from './add-spec-schema.vue';
import { useSpecSchemasColumns, useSpecSchemasFilterSchema } from './data';
import SchmaGroup from './schema-group.vue';
import SpecSchemaCalc from './spec-schema-calc.vue';
import SpecSchemaField from './spec-schema-field.vue';

const selectGroup = ref<string>('');
const currentChange = (row: SpecSchemasApi.SpecGroups) => {
  if (!row) {
    selectGroup.value = '';
    return;
  }
  selectGroup.value = row.SPECSCHEMAGROUP;
  gridApi.query();
};

const colums = useSpecSchemasColumns();
const filterSchema = useSpecSchemasFilterSchema();
const queryData = async () => {
  if (!selectGroup.value) {
    return [];
  }
  return await $getSpecSchemasApi({ specSchemaGroup: selectGroup.value });
};

const gridOptions = {
  pagerConfig: {
    enabled: false,
  },
};
const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SpecSchemasApi.SpecSchemas>(
  colums,
  filterSchema,
  queryData,
  gridOptions,
);

async function onDelete() {
  const checkOrig: number[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.ORIGREC) as number[]) || [];

  if (checkOrig.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  await $deleteSchemaNameApi(checkOrig);
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSpecSchemaForm,
  destroyOnClose: true,
});

const [CalcDrawer, calcDrawerApi] = useVbenDrawer({
  connectedComponent: SpecSchemaCalc,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const [FieldDrawer, fieldDrawerApi] = useVbenDrawer({
  connectedComponent: SpecSchemaField,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onCreate() {
  formModalApi.setData({ specSchemaGroup: selectGroup.value }).open();
}

function onRefresh() {
  gridApi.query();
}

function onActionDetail(row: SpecSchemasApi.SpecSchemas) {
  calcDrawerApi.setData({ specSchema: row }).open();
}

function onFieldDetail(row: SpecSchemasApi.SpecSchemas) {
  fieldDrawerApi.setData({ specSchema: row }).open();
}

const actionBtnSize = ref<SizeType>('small');
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <FieldDrawer />
    <CalcDrawer />
    <Row>
      <!-- 左边部门选择 -->
      <Col
        v-bind="{ xs: 24, sm: 24, md: 24, lg: 8 }"
        class="h-[calc(100vh-120px)]"
      >
        <SchmaGroup @select="currentChange" />
      </Col>
      <Col v-bind="{ xs: 24, sm: 24, md: 24, lg: 16 }">
        <Grid>
          <template #toolbar-actions>
            <Space :size="[4, 0]" wrap>
              <Button type="primary" @click="onCreate">
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button type="primary" danger @click="onDelete">
                {{ $t('ui.actionTitle.delete') }}
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent(row)">
                {{ $t('commons.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('commons.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button
                type="link"
                @click="editRowEvent(row)"
                :size="actionBtnSize"
              >
                {{ $t('commons.edit') }}
              </Button>
              <Button
                type="link"
                @click="onActionDetail(row)"
                :size="actionBtnSize"
              >
                {{ $t('business-static-tables.specSchema.action') }}
              </Button>
              <Button
                type="link"
                @click="onFieldDetail(row)"
                :size="actionBtnSize"
              >
                {{ $t('commons.field') }}
              </Button>
            </template>
          </template>
        </Grid>
      </Col>
    </Row>
  </Page>
</template>

<style lang="less" scoped></style>
