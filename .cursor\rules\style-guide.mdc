---
description: 
globs: 
alwaysApply: false
---
# Vue Vben Admin 样式开发规范

## 技术栈

- Tailwind CSS - 原子化 CSS 框架
- Less - CSS 预处理器
- PostCSS - CSS 后处理器
- CSS Variables - 主题定制
- CSS Modules - 样式隔离

## 目录结构

```bash
packages/
├── styles/              # 全局样式
│   ├── src/
│   │   ├── base/      # 基础样式
│   │   ├── themes/    # 主题样式
│   │   └── vars/      # 变量定义
│   └── index.less     # 样式入口
└── @core/
    └── base/
        └── design/    # 设计相关样式
```

## 样式规范

### 1. 全局样式

1. 变量定义 (`vars/`)
```less
// vars/colors.less
:root {
  // 品牌色
  --primary-color: #0960bd;
  --primary-color-hover: #0960bd;
  
  // 功能色
  --success-color: #55D187;
  --error-color: #ED6F6F;
  --warning-color: #EFBD47;
  
  // 中性色
  --text-color: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
}
```

2. 主题样式 (`themes/`)
```less
// themes/dark.less
[data-theme='dark'] {
  --bg-color: #151515;
  --component-bg: #1d1d1d;
  --text-color: rgba(255, 255, 255, 0.85);
}
```

### 2. 组件样式

1. 组件样式隔离
```vue
<style lang="less" module>
.componentName {
  // 使用 CSS Modules
}
</style>
```

2. 组件变量定义
```less
.component {
  // 组件级变量
  --component-height: 32px;
  --component-padding: 12px;
  
  // 继承全局变量
  color: var(--text-color);
  background: var(--component-bg);
}
```

### 3. Tailwind 使用规范

1. 配置文件 (`tailwind.config.mjs`)
```js
export default {
  // 继承基础配置
  presets: [require('@vben/tailwind-config/base')],
  // 自定义配置
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary-color)',
      },
    },
  },
}
```

2. 原子类使用
```vue
<template>
  <div class="flex items-center p-4 bg-white dark:bg-dark">
    <span class="text-primary font-medium">内容</span>
  </div>
</template>
```

### 4. 响应式设计

1. 断点定义
```less
// Tailwind 断点
sm: '640px',
md: '768px',
lg: '1024px',
xl: '1280px',
2xl: '1536px'
```

2. 响应式样式
```less
.component {
  @apply w-full md:w-1/2 lg:w-1/3;
  
  @screen xl {
    max-width: 1200px;
  }
}
```

### 5. 命名规范

1. BEM 命名规范
```less
.block {
  &__element {
    &--modifier {
      // ...
    }
  }
}
```

2. 状态类命名
```less
.is-active
.is-disabled
.is-loading
.has-error
```

## 最佳实践

### 1. 样式优先级

1. 使用 Tailwind 原子类
2. 使用 CSS Modules
3. 必要时使用 BEM
4. 避免使用 !important

### 2. 主题定制

1. 使用 CSS 变量
2. 按功能分类变量
3. 提供默认值
4. 支持暗黑模式

### 3. 性能优化

1. 合理使用选择器
2. 避免深层嵌套
3. 提取公共样式
4. 按需加载样式

### 4. 开发工具

1. 样式检查
```bash
# 运行 stylelint
pnpm lint:stylelint
```

2. 样式格式化
```bash
# 格式化样式文件
pnpm format
```
