---
description: 
globs: 
alwaysApply: false
---
# Vue Vben Admin 项目架构

## 目录结构总览

```bash
.
├── Dockerfile # Docker 镜像构建文件
├── README.md # 项目说明文档
├── apps # 项目应用目录
│   ├── backend-mock # 后端模拟服务应用
│   ├── web-antd # 基于 Ant Design Vue 的前端应用
│   ├── web-ele # 基于 Element Plus 的前端应用
│   └── web-naive # 基于 Naive UI 的前端应用
├── build-local-docker-image.sh # 本地构建 Docker 镜像脚本
├── cspell.json # CSpell 配置文件
├── docs # 项目文档目录
├── eslint.config.mjs # ESLint 配置文件
├── internal # 内部工具目录
│   ├── lint-configs # 代码检查配置
│   │   ├── commitlint-config # Commitlint 配置
│   │   ├── eslint-config # ESLint 配置
│   │   ├── prettier-config # Prettier 配置
│   │   └── stylelint-config # Stylelint 配置
│   ├── node-utils # Node.js 工具
│   ├── tailwind-config # Tailwind 配置
│   ├── tsconfig # 通用 tsconfig 配置
│   └── vite-config # 通用Vite 配置
├── package.json # 项目依赖配置
├── packages # 项目包目录
│   ├── @core # 核心包
│   │   ├── base # 基础包
│   │   │   ├── design # 设计相关
│   │   │   ├── icons # 图标
│   │   │   ├── shared # 共享
│   │   │   └── typings # 类型定义
│   │   ├── composables # 组合式 API
│   │   ├── preferences # 偏好设置
│   │   └── ui-kit # UI 组件集合
│   │       ├── layout-ui # 布局 UI
│   │       ├── menu-ui  # 菜单 UI
│   │       ├── shadcn-ui # shadcn UI
│   │       └── tabs-ui # 标签页 UI
│   ├── constants # 常量
│   ├── effects # 副作用相关包
│   │   ├── access # 访问控制
│   │   ├── plugins # 第三方大型依赖插件
│   │   ├── common-ui # 通用 UI
│   │   ├── hooks # 组合式 API
│   │   ├── layouts # 布局
│   │   └── request # 请求
│   ├── icons # 图标
│   ├── locales # 国际化
│   ├── preferences  # 偏好设置
│   ├── stores # 状态管理
│   ├── styles # 样式
│   ├── types # 类型定义
│   └── utils # 工具
├── playground # 演示目录
├── pnpm-lock.yaml # pnpm 锁定文件
├── pnpm-workspace.yaml # pnpm 工作区配置文件
├── scripts # 脚本目录
│   ├── turbo-run # Turbo 运行脚本
│   └── vsh # VSH 脚本
├── stylelint.config.mjs # Stylelint 配置文件
├── turbo.json # Turbo 配置文件
├── vben-admin.code-workspace # VS Code 工作区配置文件
└── vitest.config.ts # Vite 配置文件
```

## 核心架构说明

### 应用架构 (`apps/`)
每个应用都是独立的，可以：
- 单独运行和构建
- 独立测试和部署
- 使用不同的组件库
- 维护自己的路由和状态

### 包架构 (`packages/`)

#### 1. 核心功能 (`@core/`)
- 基础设施和类型定义
- 组合式 API 封装
- UI 组件库集合

#### 2. 副作用管理 (`effects/`)
- 权限和访问控制
- 网络请求处理
- 布局和通用组件

#### 3. 工具和资源
- 国际化资源
- 状态管理
- 工具函数
- 类型定义

### 开发工具 (`internal/`)
- 代码规范配置
- 构建工具配置
- 类型系统配置

## 配置文件说明

### 工程配置
- `package.json` - 项目配置
- `pnpm-workspace.yaml` - 工作区配置
- `turbo.json` - Turbo 配置
- `vite.config.ts` - Vite 配置

### 代码规范
- `eslint.config.mjs` - ESLint 配置
- `stylelint.config.mjs` - 样式检查
- `cspell.json` - 拼写检查
- `.prettierrc.mjs` - 格式化配置

### 环境配置
- `.env.development` - 开发环境
- `.env.production` - 生产环境
- `.env.test` - 测试环境

## 技术栈

### 核心技术
- Vue 3 - 核心框架
- TypeScript - 开发语言
- Vite - 构建工具
- Pnpm - 包管理器
- Turbo - 任务调度

### UI 框架支持
- Ant Design Vue
- Naive UI
- Element Plus

### 开发工具链
- ESLint - 代码检查
- Prettier - 代码格式化
- Stylelint - 样式检查
- Commitlint - 提交规范
- Tailwind CSS - 原子化 CSS
- Vitest - 单元测试

## 路径别名

项目使用 Node.js 的 subpath imports：

1. `package.json` 配置：
```json
{
  "imports": {
    "#/*": "./src/*"
  }
}
```

2. `tsconfig.json` 配置：
```json
{
  "compilerOptions": {
    "paths": {
      "#/*": ["src/*"]
    }
  }
}
```
