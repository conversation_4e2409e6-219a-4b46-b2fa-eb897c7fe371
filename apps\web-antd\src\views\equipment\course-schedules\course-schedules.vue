<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CourseSchedulesFormApi } from '#/api/equipment/course-schedules';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  Button,
  DatePicker,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';

import { courseColumns } from './course-schedules-data';

const viewType = ref('');
const options = ref([
  { value: '网格', label: '网格' },
  { value: '月', label: '月' },
  { value: '周', label: '周' },
  { value: '工作周', label: '工作周' },
  { value: '天', label: '天' },
]);
const gridOptions: VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    columns: courseColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    data: [
      {
        courseCode: 'ID1',
        courseName: '类型一',
        CALMECHANISM: '名称一',
        CALPARAM: '状态一',
        ORIGREC: 0,
        NAME: '',
        EFFECT: '',
        TYPE: '',
        SORTER: 0,
      },
      {
        courseCode: 'ID1',
        courseName: '类型一',
        CALMECHANISM: '名称一',
        CALPARAM: '状态一',
        ORIGREC: 0,
        NAME: '',
        EFFECT: '',
        TYPE: '',
        SORTER: 0,
      },
      {
        courseCode: 'ID1',
        courseName: '类型一',
        CALMECHANISM: '名称一',
        CALPARAM: '状态一',
        ORIGREC: 0,
        NAME: '',
        EFFECT: '',
        TYPE: '',
        SORTER: 0,
      },
    ],

    // proxyConfig: {
    //   ajax: {
    //     query: async () => {
    //       return;
    //     },
    //   },
    // },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'origrec',
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: {},
      search: true,
      zoom: true,
    },
  };
const [Grid] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  tableTitle: '培训计划',
});
const startTime = ref<string>('');
const endTime = ref<string>('');
const activeKey = ref('课程方法');
const tabList = ref([
  { title: '课程方法', page: 'CourseMethod' },
  { title: '操作历史', page: 'CourseContent' },
]);
</script>
<template>
  <Page auto-content-height>
    <Grid class="h-2/3">
      <template #toolbar-actions>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="addCourse">
            {{ $t('equipment.add') }}
          </Button>
          <Button type="primary" @click="deleteCourse">
            {{ $t('equipment.delete') }}
          </Button>
          <Button type="primary" @click="printReport">
            {{ $t('equipment.course-form.printReport') }}
          </Button>
          <Button type="primary" @click="addCourse">
            {{ $t('equipment.add') }}
          </Button>
          <Button type="primary" @click="deleteCourse">
            {{ $t('equipment.delete') }}
          </Button>
          <Button type="primary" @click="printReport">
            {{ $t('equipment.course-form.printReport') }}
          </Button>
        </Space>
      </template>
      <template #toolbar-tools>
        <Space :size="[8, 0]" wrap>
          <span>视图：</span>
          <Select v-model:value="viewType" style="width: 150px">
            <SelectOption
              v-for="item in options"
              :value="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </SelectOption>
          </Select>
          <span>开始时间</span>
          <DatePicker v-model:value="startTime" />至
          <DatePicker v-model:value="endTime" />
          <Button type="primary" @click="printReport">
            {{ $t('equipment.course-form.printReport') }}
          </Button>
        </Space>
      </template>
    </Grid>
    <div class="flex h-1/3">
      <Grid height="100%" class="w-1/2">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            <Button type="primary" @click="addCourse">
              {{ $t('equipment.add') }}
            </Button>
            <Button type="primary" @click="deleteCourse">
              {{ $t('equipment.delete') }}
            </Button>
            <Button type="primary" @click="printReport">
              {{ $t('equipment.course-form.printReport') }}
            </Button>
            <Button type="primary" @click="addCourse">
              {{ $t('equipment.add') }}
            </Button>
            <Button type="primary" @click="deleteCourse">
              {{ $t('equipment.delete') }}
            </Button>
            <Button type="primary" @click="printReport">
              {{ $t('equipment.course-form.printReport') }}
            </Button>
          </Space>
        </template>
      </Grid>
      <div class="h-1/3 w-1/2">
        <Tabs v-model:active-key="activeKey" class="w-full bg-white">
          <TabPane
            v-for="item in tabList"
            :key="item.title"
            :tab="item.title"
            class="w-full"
          />
        </Tabs>
        <Grid height class="h-5/6" />
      </div>
    </div>
  </Page>
</template>
