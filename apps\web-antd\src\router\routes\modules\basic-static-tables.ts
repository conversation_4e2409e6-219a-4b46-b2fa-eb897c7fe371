import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: $t('basic-static-tables.title'),
    },
    name: 'BasicStaticTable',
    path: '/basicstatictable',
    children: [
      {
        meta: {
          title: $t('basic-static-tables.material-types.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'material-types',
        path: '/basic-static-tables/material-types',
        component: () =>
          import(
            '#/views/basic-static-tables/material-types/material-types.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.client-categories.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'client-categories',
        path: '/basic-static-tables/client-categories',
        component: () =>
          import(
            '#/views/basic-static-tables/client-categories/client-categories.vue'
          ),
      },
      {
        path: '/basicStatic/lookups',
        name: 'lookups',
        meta: {
          icon: 'mdi:table',
          title: $t('basicStatic.lookups.title'),
        },
        component: () =>
          import('#/views/basic-static-tables/generic-meta-data/lookup.vue'),
      },
      {
        path: '/basicStatic/regions',
        name: 'regions',
        meta: {
          icon: 'mdi:list-box-outline',
          title: $t('basicStatic.regions.title'),
        },
        component: () =>
          import('#/views/basic-static-tables/regions/regions.vue'),
      },
      {
        path: '/basicStatic/jobDescriptions',
        name: 'jobDescriptions',
        meta: {
          icon: 'mdi:table',
          title: $t('basicStatic.jobDescriptions.jobDescriptions'),
        },
        component: () =>
          import(
            '#/views/basic-static-tables/job-descriptions/job-descriptions.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.equip-types.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'equip-types',
        path: '/basic-static-tables/equip-types',
        component: () =>
          import('#/views/basic-static-tables/equip-types/equip-types.vue'),
      },
      {
        meta: {
          title: $t('basic-static-tables.qc-types.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'qc-types',
        path: '/basic-static-tables/qc-types',
        component: () =>
          import('#/views/basic-static-tables/qc-types/qc-types.vue'),
      },
      {
        meta: {
          title: $t('basic-static-tables.test-categories.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'test-categories',
        path: '/basic-static-tables/test-categories',
        component: () =>
          import(
            '#/views/basic-static-tables/test-categories/test-categories.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.test-plan-groups.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'test-plan-groups',
        path: '/basic-static-tables/test-plan-groups',
        component: () =>
          import(
            '#/views/basic-static-tables/test-plan-groups/test-plan-groups.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.spec-categories.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'spec-categories',
        path: '/basic-static-tables/spec-categories',
        component: () =>
          import(
            '#/views/basic-static-tables/spec-categories/spec-categories.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.supplier.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'supplier',
        path: '/basic-static-tables/supplier',
        component: () =>
          import('#/views/basic-static-tables/supplier/supplier.vue'),
      },
      {
        meta: {
          title: $t('basic-static-tables.sub-location-type.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'sub-location-type',
        path: '/basic-static-tables/sub-location-type',
        component: () =>
          import(
            '#/views/basic-static-tables/sub-location-type/sub-location-type.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.location-type.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'location-type',
        path: '/basic-static-tables/location-type',
        component: () =>
          import('#/views/basic-static-tables/location-type/location-type.vue'),
      },
      {
        meta: {
          title: $t('basic-static-tables.measure-types.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'measure-types',
        path: '/basic-static-tables/units-management',
        component: () =>
          import(
            '#/views/basic-static-tables/units-management/measure-types.vue'
          ),
      },
      {
        meta: {
          title: $t('basic-static-tables.conditions.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'conditions',
        path: '/basic-static-tables/conditions',
        component: () =>
          import('#/views/basic-static-tables/conditions/conditions.vue'),
      },
      {
        path: '/basicStatic/rpsCalendar',
        name: 'rpsCalendar',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.rpswaCalendar.title'),
        },
        component: () =>
          import('#/views/basic-static-tables/rps-calendar/rps-calendar.vue'),
      },
      {
        path: '/basicStatic/holidays',
        name: 'holidays',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.holidays.menu'),
        },
        component: () =>
          import('#/views/basic-static-tables/holidays/holidays-calendar.vue'),
      },
      {
        path: '/basicStatic/sampleType',
        name: 'sampleType',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.sampleType.sampleType'),
        },
        component: () =>
          import('#/views/basic-static-tables/sample-type/sample-type.vue'),
      },
      {
        path: '/basicStatic/solutionType',
        name: 'solutionType',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.solutionType.solutionType'),
        },
        component: () =>
          import('#/views/basic-static-tables/solution-type/solution-type.vue'),
      },
      {
        path: '/basicStatic/soluctionInfo',
        name: 'soluctionInfo',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.solutionInfo.menu'),
        },
        component: () =>
          import(
            '#/views/basic-static-tables/soluction-infomation/soluction-main.vue'
          ),
      },
      {
        path: '/basicStatic/virusType',
        name: 'virusType',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.virusType.menu'),
        },
        component: () =>
          import(
            '#/views/basic-static-tables/prompt-virus-type/prompt-virus-type.vue'
          ),
      },
      {
        path: '/basicStatic/checkList',
        name: 'checkList',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.checkList.menu'),
        },
        component: () =>
          import('#/views/basic-static-tables/check-list/check-list.vue'),
      },
      {
        path: '/basicStatic/chsTranslate',
        name: 'chsTranslate',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.chsTranslate.menu'),
        },
        component: () =>
          import('#/views/basic-static-tables/chs-translate/chs-translate.vue'),
      },
      {
        path: '/basicStatic/inspectionItem',
        name: 'inspectionItem',
        meta: {
          icon: 'mdi:table',
          title: $t('basic-static-tables.inspectionItem.menu'),
        },
        component: () =>
          import(
            '#/views/basic-static-tables/inspection-item/inspection-item.vue'
          ),
      },
    ],
  },
];
export default routes;
