import { callServer, getDataSet } from '#/api/core/witlab';

export namespace ReceiveInLabApi {
  export interface ReceiveOrders {
    ORIGREC: number;
    TAT: string;
    FLDISPSTATUS: string;
    RECEIVEDSTS: string;
    BATCHNO: string;
    SAMPLE_NAME: string;
    MATNAME: string;
    STATUS: string;
    ORDNO: string;
    LOGDATE: Date;
    SAMPDATE: Date;
    COMPDAT: Date;
    SAMPLEDBY: string;
    SAMPGRTYPE: string;
    FLSTATUS: string;
    ACTUALVOL: string;
    CLSAMPNO: string;
    RETEST_BATCHID: string;
    XORDNO: string;
    Value: number;
    Text: string;
    LOCATIONCODE: string;
    LOCATION_NAME: string;
    SAMPLE_TYPE: string;
    FOLDERNO: string;
  }
}

async function getPendingReceiptdg() {
  return getDataSet('ReceiveInLab.getPendingReceiptdg', []);
}

async function getSampleData(
  sOpeningMode: string,
  nOrigrecs: number[],
  sLocationCode: string,
) {
  return getDataSet('ReceiveInLab.getSampleData', [
    sOpeningMode,
    nOrigrecs,
    sLocationCode,
  ]);
}

async function receiveInLabMulti(
  sType: string,
  aSampleData: string[][],
  dReceiveDate: Date,
  sOpeningMode: string,
) {
  return await callServer('ReceiveInLab.receiveInLabMulti', [
    sType,
    aSampleData,
    dReceiveDate,
    sOpeningMode,
  ]);
}

async function updReceiveAll(aOrdNos: string[]) {
  return await callServer('ReceiveInLab.UpdReceiveAll', [aOrdNos]);
}

async function returnToLogin(sOrdNo: string) {
  return await callServer('ReceiveInLab.returnToLogin', [sOrdNo]);
}

async function getPromptForSample(sOpeningMode: string, sType: string) {
  return getDataSet('ReceiveInLab.getPromptForSample', [sOpeningMode, sType]);
}

async function getSamplingRequirements(sOrdNo: string) {
  return getDataSet('ReceiveInLab.samplingRequirements', [sOrdNo]);
}

async function getTests(sOrdNo: string) {
  return getDataSet('ReceiveInLab.getTests', [sOrdNo]);
}

async function removedSamplingRequirement(
  sOrigrec: number[],
  sComment: string,
  sOrdNo: string,
) {
  return await callServer('ReceiveInLab.removedSamplingRequirement', [
    sOrigrec,
    sComment,
    sOrdNo,
  ]);
}

async function chkActiveNum(sOrdNo: string) {
  return await callServer('ReceiveInLab.ChkActiveNum', [sOrdNo]);
}

async function finishAliquot(sOrdNo: string) {
  return await callServer('ReceiveInLab.FinishAliquot', [sOrdNo]);
}

export {
  chkActiveNum,
  finishAliquot,
  getPendingReceiptdg,
  getPromptForSample,
  getSampleData,
  getSamplingRequirements,
  getTests,
  receiveInLabMulti,
  removedSamplingRequirement,
  returnToLogin,
  updReceiveAll,
};
