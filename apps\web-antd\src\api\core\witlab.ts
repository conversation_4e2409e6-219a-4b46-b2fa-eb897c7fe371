import { defaultResponseInterceptor, RequestClient } from '@vben/request';

// import { baseRequestClient } from '#/api/request';
const baseRequestClient = new RequestClient({
  baseURL: import.meta.env.VITE_WITLAB_API_URL,
  responseReturn: 'data',
});

// 处理返回的响应数据格式
baseRequestClient.addResponseInterceptor(
  defaultResponseInterceptor({
    codeField: 'code',
    dataField,
    successCode: 200,
  }),
);

export namespace WitLabApi {
  export interface WitLabRequestParams {
    scriptName: string;
    params: Array<any>;
  }

  export interface DataSet {
    Tables: Array<{
      Rows: Array<any>;
    }>;
  }

  export interface WitLabResponseResult {
    result: any | Array<any> | DataSet;
    success: boolean;
  }
}

async function callWitlab(
  type: 'DataSource' | 'ServerScript',
  requestParams: WitLabApi.WitLabRequestParams,
) {
  return baseRequestClient.post<WitLabApi.WitLabResponseResult>(
    '/WitLab/v1/WitLab/Proxy',
    {
      type,
      ...requestParams,
    },
  );
}

export async function callServer(scriptName: string, params: Array<any>) {
  const limsParams: WitLabApi.WitLabRequestParams = {
    scriptName,
    params,
  };
  const respData = await callWitlab('ServerScript', limsParams);
  return respData.result;
}

export async function getDataSet(scriptName: string, params: Array<any>) {
  const limsParams: WitLabApi.WitLabRequestParams = {
    scriptName,
    params,
  };
  const tableData = await callWitlab('DataSource', limsParams);
  // return getDataField(tableData);
  return tableData.result;
}

export async function getDataSetNoPage(scriptName: string, params: Array<any>) {
  const limsParams: WitLabApi.WitLabRequestParams = {
    scriptName,
    params,
  };
  const respData = await callWitlab('DataSource', limsParams);
  const results = respData.result.items;
  return results;
}

// function getDataField(response: WitLabApi.WitLabResponseResult) {
//   const results = response.result as WitLabApi.DataSet;
//   return {
//     items: results.Tables?.[0]?.Rows ?? [],
//     total: results.Tables?.[0]?.Rows?.length ?? 0,
//   };
// }

function dataField(response: any) {
  const respData = response.data;
  const respResult = respData.result;
  if (
    respResult &&
    typeof respResult === 'object' &&
    'Tables' in respResult &&
    Array.isArray(respResult.Tables)
  ) {
    return {
      result: {
        items: respResult.Tables?.[0]?.Rows ?? [],
        total: respResult.Tables?.[0]?.Rows?.length ?? 0,
      },
      success: respData.success,
    };
  }
  return respData;
}
