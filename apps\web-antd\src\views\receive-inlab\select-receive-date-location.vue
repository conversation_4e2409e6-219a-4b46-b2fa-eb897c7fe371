<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getLocationList } from '#/api/materials-management/location-management';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref();

// 树形数据处理
const treeData = ref([]);
const isLoading = ref(false);

// 数据转换函数：将API返回的扁平数据转换为树形结构
const transformToTreeData = (flatData: any[]) => {
  if (!flatData || flatData.length === 0) return [];

  const map = new Map();
  const roots: any[] = [];

  // 第一遍遍历：创建所有节点
  flatData.forEach((item) => {
    const node = {
      label: item.TEXTMEMBER,
      value: item.VALUEMEMBER,
      key: item.NAMEMEMBER,
      parentId: item.PARENTMEMBER,
      children: [],
      // 保留原始数据
      ...item,
    };
    map.set(item.NAMEMEMBER, node);
  });

  // 第二遍遍历：建立父子关系
  flatData.forEach((item) => {
    const node = map.get(item.NAMEMEMBER);
    if (item.NAMEMEMBER && map.has(item.PARENTMEMBER)) {
      // 有父节点，添加到父节点的children中
      const parent = map.get(item.PARENTMEMBER);
      parent.children.push(node);
    } else {
      // 没有父节点或父节点不存在，作为根节点
      roots.push(node);
    }
  });

  return roots;
};

// 异步加载位置数据
const loadLocationData = async (parentValue?: string) => {
  try {
    isLoading.value = true;
    const result = await getLocationList('SITE1');

    if (result && result.items) {
      if (parentValue) {
        // 动态加载子节点
        return transformToTreeData(
          result.items.filter((item: any) => item.PARENTMEMBER === parentValue),
        );
      } else {
        // 初始加载所有数据
        const transformedData = transformToTreeData(result.items);
        treeData.value = transformedData;
        console.log('transformedData', transformedData);
        return transformedData;
      }
    }
    return [];
  } catch (error) {
    console.error('加载位置数据失败:', error);
    message.error('加载位置数据失败');
    return [];
  } finally {
    isLoading.value = false;
  }
};

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  layout: 'vertical',
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'COMPDAT',
      label: $t('receive-inlab.compdat'),
      rules: 'required',
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'LOCATIONCODE',
      label: $t('receive-inlab.locationcode'),
      rules: 'required',
      componentProps: {
        allowClear: true,
        placeholder: '请选择位置',
        showSearch: true,
        treeDefaultExpandAll: false,
        treeDefaultExpandedKeys: [],

        // API配置
        api: loadLocationData,
        immediate: true,

        // 字段映射配置
        labelField: 'TEXTMEMBER',
        valueField: 'VALUEMEMBER',
        childrenField: 'children',

        // TreeSelect特有配置
        fieldNames: {
          label: 'TEXTMEMBER',
          value: 'VALUEMEMBER',
          children: 'children',
        },

        // 搜索配置
        treeNodeFilterProp: 'TEXTMEMBER',
        filterTreeNode: (inputValue: string, treeNode: any) => {
          if (!inputValue) return true;
          const label = treeNode.TEXTMEMBER || treeNode.label || '';
          return label.toLowerCase().includes(inputValue.toLowerCase());
        },

        // 动态加载配置
        loadData: async (treeNode: any) => {
          // 动态加载子节点
          if (treeNode.children && treeNode.children.length > 0) {
            return; // 已有子节点，不需要加载
          }

          try {
            const children = await loadLocationData(treeNode.key);
            treeNode.children = children;
          } catch (error) {
            console.error('动态加载子节点失败:', error);
          }
        },

        // 事件处理
        onSelect: (value: string, node: any) => {
          console.log('选中节点:', { value, node });
        },

        onTreeExpand: (expandedKeys: string[]) => {
          console.log('展开的节点:', expandedKeys);
        },

        // 加载状态
        loading: isLoading,
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formData.value = modalApi.getData<Record<string, any>>();
      // 初始化加载树形数据
      await loadLocationData();
    }
  },
  title: '接收样品',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    // const data = await formApi.getValues();
    // console.log(data);

    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
