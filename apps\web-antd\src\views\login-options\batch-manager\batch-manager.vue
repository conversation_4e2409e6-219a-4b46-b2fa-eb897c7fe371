<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

// 添加了 onMounted 和 reactive 的导入
import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import {
  cancel,
  getBatchesList,
  StartBacthTwo,
  StartBatch,
} from '#/api/login-options/batch-manager';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useBatchesColumns,
  useBatchesFilterSchema,
} from './batch-manager-data';
import BatchRecipe from './batch-recipe.vue';
import SampleRequirement from './batch-requirement.vue';
import SampleOrders from './batch-sample-orders.vue';
// import AddForm from './raw-material-tp.vue';
// import AddForm from './adhoc-tp.vue';
// import AddForm from './inspection-tp.vue';
import AddForm from './prompt-for-product-step.vue';

const activeKey = ref('1');
const colums = useBatchesColumns();
const filterSchema = useBatchesFilterSchema();
const sMode = ref('Create');
const queryData = async () => {
  return getBatchesList(sMode.value, '', '');
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.Batches>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 登录
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddForm,
});

async function onCreate() {
  formModalApi.setData(null).open();
}

// 启动
async function onStart() {
  // 获取选中行
  const batch = gridApi.grid?.getCurrentRecord();
  if (!batch) return;

  const batchId = batch.BATCHID;
  const sType = batch.TYPE;
  try {
    await confirm({
      title: '确认启动',
      content: `确定要启动批次 ${batchId} 吗？`,
      icon: 'warning',
      centered: false,
    });
    // 免检类批次
    if (sType === 'EPIM') {
      await StartBacthTwo(batchId);
    } else {
      const nStartBatch = await StartBatch(batchId);
      switch (nStartBatch) {
        case -400: {
          message.warn($t('login-options.batchManager.wrnSpecifications'));
          return;
        }
        case -300: {
          message.warn($t('login-options.batchManager.errSpecifications'));
          return;
        }
        case -200: {
          message.warn($t('login-options.batchManager.UNKNOWN_ERROR'));
          return;
        }
        case -101: {
          message.warn($t('login-options.batchManager.plannedPrecursors'));
          return;
        }
        case -100: {
          message.warn($t('login-options.batchManager.noBatchRecipe'));
          return;
        }
      }
    }
    message.success('启动成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 取消
async function onCancel() {
  // 获取选中行
  const batch = gridApi.grid?.getCurrentRecord();
  if (!batch) return;

  const batchId = batch.BATCHID;
  const sType = 'BATCH';
  try {
    await confirm({
      title: '确认取消',
      content: `确定要取消批次 ${batchId} 吗？`,
      icon: 'warning',
      centered: false,
    });

    await cancel(batchId, sType);
    message.success('操作成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <div class="my-2 h-[420px] w-full">
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onCreate">
              {{ $t('login-options.login') }}
            </Button>
            <Button type="primary" danger @click="onCancel">
              {{ $t('login-options.cancel') }}
            </Button>
            <Button type="default">
              {{ $t('login-options.copy') }}
            </Button>
            <Button type="default" @click="onStart">
              {{ $t('login-options.start') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('login-options.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('login-options.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('login-options.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane key="1" tab="样品/测试">
        <SampleOrders :current-test-row="CurrentRow" :mode="sMode" />
      </TabPane>
      <TabPane key="2" tab="配方">
        <BatchRecipe :current-test-row="CurrentRow" />
      </TabPane>
      <TabPane key="3" tab="取样要求">
        <SampleRequirement :current-test-row="CurrentRow" />
      </TabPane>
    </Tabs>
  </Page>
</template>
