<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addRawMatSample,
  // calculateNumUnPack,
  cbMfgEquip,
  getMatInfo,
  getRequestReason,
  getSampGroupsByProdGrpAndPlant,
  getSuppliersData,
  getTestPlanGroups,
  rawMatSpCodecb,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

// const formRef = ref();
const sTestPlanGroup = ref<string>('');
const sSampleGroupCode = ref<string>('');
const sRawStorageCondition = ref<string>('');

async function onRawMaterialSubmit(values: Record<string, any>) {
  await rawMaterialFormApi.validateAndSubmitForm();
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    // modalApi.lock();

    const data = values as BatcheManagerApi.Batches;
    data.SAMPLEGROUPCODE = sSampleGroupCode.value;
    data.STORAGE_CONDITION = sRawStorageCondition.value;
    await addRawMatSample(data);
    emit('success');
    // modalApi.close();
    message.success({
      content: '操作成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `操作失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    // modalApi.close();
  } finally {
    // modalApi.unlock();
  }
}

const [RawMaterialForm, rawMaterialFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onRawMaterialSubmit,
  layout: 'vertical',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getTestPlanGroups.bind(null, '', 'RAWMAT'),
        labelField: 'PRODGROUP',
        valueField: 'PRODGROUP',
        immediate: true,
        autoSelect: true,
        onChange: async (value: string) => {
          sTestPlanGroup.value = value;
        },
      },
      fieldName: 'PRODGROUP',
      label: $t('login-options.batchManager.prodGroup'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await cbMfgEquip('RAWMAT');
          return res.items.map((item: { Text: string; Value: string }) => ({
            label: item.Text,
            value: item.Value,
          }));
        },
        immediate: true,
        onChange: async (value: string) => {
          const res = await getSampGroupsByProdGrpAndPlant(
            sTestPlanGroup.value,
            'RAWMAT',
            value,
            '',
          );
          const options = res.items.map(
            (item: { Text: string; Value: string }) => ({
              label: item.Text,
              value: item.Value,
            }),
          );
          rawMaterialFormApi.updateSchema([
            {
              fieldName: 'SAMPLEGROUPCODE', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
      fieldName: 'PLANT',
      label: $t('login-options.batchManager.plant'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        immediate: true,
        onChange: async (value: string) => {
          const str = value.split('>');
          sSampleGroupCode.value = str[0] || '';

          const res = await rawMatSpCodecb(sSampleGroupCode.value, '', '');
          const options = res.items.map((item: { MATCODE: string }) => ({
            label: item.MATCODE,
            value: item.MATCODE,
          }));
          rawMaterialFormApi.updateSchema([
            {
              fieldName: 'MATCODE', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
      fieldName: 'SAMPLEGROUPCODE',
      label: $t('login-options.batchManager.sampleGroupCode'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'PROCESS',
      label: $t('login-options.batchManager.process'),
    },
    {
      component: 'Input',
      fieldName: 'RETEST_BATCHID',
      label: $t('login-options.batchManager.retestBatchId'),
    },
    {
      component: 'Input',
      fieldName: 'RETESTORDNO',
      label: $t('login-options.batchManager.retestOrderNo'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getRequestReason();
          return res.items.map((item: { TEXT: string; VALUE: string }) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        immediate: true,
      },
      fieldName: 'REQUEST_REASON',
      label: $t('login-options.batchManager.requestReason'),
    },
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.batchManager.entryNumber'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'MFG',
      label: $t('login-options.batchManager.mfg'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'SUPPBATCHNO',
      label: $t('login-options.batchManager.suppbatchno'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'SUPPCODE',
      label: $t('login-options.batchManager.suppnam'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'MATCODE',
      componentProps: {
        onChange: async (value: string) => {
          const matinfo = await getMatInfo(value);
          if (matinfo.length > 0) {
            const matName = matinfo[0];
            const rawPackingSpec = matinfo[1];
            sRawStorageCondition.value = matinfo[2];
            rawMaterialFormApi.setFieldValue('MATNAME', matName);
            rawMaterialFormApi.setFieldValue('PACKING_SPEC', rawPackingSpec);

            const resMfg = await getSuppliersData(value, 'SC');
            const mfgOptions = resMfg.items.map(
              (item: { TEXT: string; Value: string }) => ({
                label: item.TEXT,
                value: item.Value,
              }),
            );
            const resSupp = await getSuppliersData(value, 'GY');
            const suppOptions = resSupp.items.map(
              (item: { TEXT: string; Value: string }) => ({
                label: item.TEXT,
                value: item.Value,
              }),
            );
            rawMaterialFormApi.updateSchema([
              {
                fieldName: 'MFG',
                componentProps: {
                  options: mfgOptions,
                },
              },
              {
                fieldName: 'SUPPCODE',
                componentProps: {
                  options: suppOptions,
                },
              },
            ]);
          }
        },
      },
      label: $t('login-options.batchManager.matcode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'MATNAME',
      label: $t('login-options.batchManager.matname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'NUM_ARRIVAL',
      label: $t('login-options.batchManager.num_arrival'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'NUM_UNPACK',
      label: $t('login-options.batchManager.num_unpack'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'PRODUCTGRADE',
      label: $t('login-options.batchManager.productgrade'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ACTUALVOL',
      label: $t('login-options.batchManager.actualVol'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ESTIMATEDVOL',
      label: $t('login-options.batchManager.rawEstimatedVol'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ESTIMATEDVOL_UNITS',
      label: $t('login-options.batchManager.estimatedVolUnits'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'VERSION_NO',
      label: $t('login-options.batchManager.version_no'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'PACKING_SPEC',
      label: $t('login-options.batchManager.packingSpec'),
    },
    {
      component: 'DatePicker',
      fieldName: 'PRODUCTION_DATE',
      label: $t('login-options.batchManager.productionDate'),
    },
    {
      component: 'DatePicker',
      fieldName: 'EXPDATE',
      label: $t('login-options.batchManager.expdate'),
    },
    {
      component: 'DatePicker',
      fieldName: 'RETEST_DATE',
      label: $t('login-options.batchManager.retest_date'),
    },
    {
      component: 'Input',
      fieldName: 'REQUEST_DEPT',
      label: $t('login-options.batchManager.requestDept'),
    },
    {
      component: 'DatePicker',
      fieldName: 'REQUEST_DATE',
      label: $t('login-options.batchManager.requestDate'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'REQUESTER',
      label: $t('login-options.batchManager.requester'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'COMMENTS',
      label: $t('login-options.batchManager.comments'),
    },
  ],
  // wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-8',
  showDefaultActions: false,
  // handleValuesChange: (changedValues, allValues) => {
  //   console.log(changedValues);
  //   console.log(allValues);
  //   if ('NUM_ARRIVAL' in changedValues) {
  //     const allValues = rawMaterialFormApi.getValues();
  //     console.log(allValues);
  //     const numUnpack = calculateNumUnPack(allValues.NUM_ARRIVAL);
  //     rawMaterialFormApi.setFieldValue('NUM_UNPACK', numUnpack);
  //   }
  // },
});

// const [Modal, modalApi] = useVbenModal({
//   destroyOnClose: true,
//   showConfirmButton: true,
//   onConfirm: async () => {
//     await rawMaterialFormApi.validateAndSubmitForm();
//   },
//   async onOpenChange(isOpen: boolean) {
//     if (isOpen) {
//       // await RawMaterialForm.resetFields();
//     }
//   },
// });

const validateAllForms = async () => {
  const log = await rawMaterialFormApi.validateAndSubmitForm();
  console.log('log', log);
};


defineExpose({ rawMaterialFormApi, onRawMaterialSubmit });
</script>

<template>
  <!-- <Modal title="原辅包登录" class="h-[1000px] w-[1200px]"> -->
  <Page>
    <RawMaterialForm />
  </Page>
  <!-- </Modal> -->
</template>
