# TreeSelect 组件使用指南

## 🎯 概述

本指南提供了在 `select-receive-date-location.vue` 文件中配置 TreeSelect 组件的完整方案，包括API数据加载、字段映射、动态子节点加载等功能。

## 📊 数据结构分析

### API返回数据结构
```typescript
interface LocationManage {
  ORIGREC: number;
  TEXTMEMBER: string;    // 显示文本
  VALUEMEMBER: string;   // 选中值
  PARENTMEMBER: string;  // 父节点ID
  NAMEMEMBER: string;
  SEQ: number;
  // ... 其他字段
}
```

### TreeSelect需要的数据结构
```typescript
interface TreeNode {
  label: string;         // 显示文本
  value: string;         // 选中值
  children?: TreeNode[]; // 子节点
  key?: string;          // 节点唯一标识
}
```

## ✅ 配置方案

### 方案一：完整功能配置（推荐）

```typescript
{
  component: 'ApiTreeSelect',
  fieldName: 'LOCATIONCODE',
  label: $t('receive-inlab.locationcode'),
  rules: 'required',
  componentProps: {
    // 基本配置
    allowClear: true,
    placeholder: '请选择位置',
    showSearch: true,
    treeDefaultExpandAll: false,
    
    // API配置
    api: loadLocationData,
    immediate: true,
    
    // 字段映射配置
    labelField: 'TEXTMEMBER',
    valueField: 'VALUEMEMBER',
    childrenField: 'children',
    
    // TreeSelect特有配置
    fieldNames: {
      label: 'TEXTMEMBER',
      value: 'VALUEMEMBER',
      children: 'children',
    },
    
    // 搜索配置
    treeNodeFilterProp: 'TEXTMEMBER',
    filterTreeNode: (inputValue: string, treeNode: any) => {
      if (!inputValue) return true;
      const label = treeNode.TEXTMEMBER || treeNode.label || '';
      return label.toLowerCase().includes(inputValue.toLowerCase());
    },
    
    // 动态加载配置
    loadData: async (treeNode: any) => {
      if (treeNode.children && treeNode.children.length > 0) return;
      
      try {
        const children = await loadLocationData(treeNode.value);
        treeNode.children = children;
      } catch (error) {
        console.error('动态加载子节点失败:', error);
      }
    },
    
    // 事件处理
    onSelect: (value: string, node: any) => {
      console.log('选中节点:', { value, node });
    },
    
    // 加载状态
    loading: isLoading,
  },
}
```

### 方案二：简化配置

```typescript
{
  component: 'ApiTreeSelect',
  fieldName: 'LOCATIONCODE',
  label: $t('receive-inlab.locationcode'),
  rules: 'required',
  componentProps: {
    // 基本配置
    allowClear: true,
    placeholder: '请选择位置',
    showSearch: true,
    
    // API配置
    api: () => getLocationList('SITE1'),
    immediate: true,
    
    // 字段映射
    labelField: 'TEXTMEMBER',
    valueField: 'VALUEMEMBER',
    childrenField: 'children',
    
    // 数据转换
    afterFetch: (data: any[]) => {
      return transformToTreeData(data);
    },
    
    // 搜索配置
    treeNodeFilterProp: 'TEXTMEMBER',
  },
}
```

## 🔧 核心功能实现

### 1. API数据加载

#### 基本API调用
```typescript
// 方式1：直接调用API
api: () => getLocationList('SITE1'),

// 方式2：自定义加载函数
api: loadLocationData,

// 方式3：带参数的API调用
api: getLocationList.bind(null, 'SITE1'),
```

#### 数据转换函数
```typescript
const transformToTreeData = (flatData: any[]) => {
  if (!flatData || flatData.length === 0) return [];
  
  const map = new Map();
  const roots: any[] = [];
  
  // 创建节点映射
  flatData.forEach(item => {
    const node = {
      label: item.TEXTMEMBER,
      value: item.VALUEMEMBER,
      key: item.VALUEMEMBER,
      parentId: item.PARENTMEMBER,
      children: [],
      ...item,
    };
    map.set(item.VALUEMEMBER, node);
  });
  
  // 建立父子关系
  flatData.forEach(item => {
    const node = map.get(item.VALUEMEMBER);
    if (item.PARENTMEMBER && map.has(item.PARENTMEMBER)) {
      const parent = map.get(item.PARENTMEMBER);
      parent.children.push(node);
    } else {
      roots.push(node);
    }
  });
  
  return roots;
};
```

### 2. 自定义字段映射

#### 基本字段映射
```typescript
// 方式1：使用 labelField、valueField
labelField: 'TEXTMEMBER',
valueField: 'VALUEMEMBER',
childrenField: 'children',

// 方式2：使用 fieldNames（TreeSelect特有）
fieldNames: {
  label: 'TEXTMEMBER',
  value: 'VALUEMEMBER',
  children: 'children',
},
```

#### 复杂字段映射
```typescript
afterFetch: (data: any[]) => {
  return data.map(item => ({
    label: `${item.TEXTMEMBER} (${item.NAMEMEMBER})`, // 组合显示
    value: item.VALUEMEMBER,
    key: item.VALUEMEMBER,
    disabled: item.STATUS === 'DISABLED', // 条件禁用
    children: item.children || [],
    ...item, // 保留原始数据
  }));
},
```

### 3. 动态子节点加载

#### 懒加载实现
```typescript
loadData: async (treeNode: any) => {
  // 检查是否已有子节点
  if (treeNode.children && treeNode.children.length > 0) {
    return;
  }
  
  try {
    // 调用API获取子节点
    const children = await loadLocationData(treeNode.value);
    
    // 设置子节点
    treeNode.children = children;
    
    // 标记为已加载
    treeNode.loaded = true;
  } catch (error) {
    console.error('动态加载子节点失败:', error);
    message.error('加载子节点失败');
  }
},
```

### 4. 搜索和过滤

#### 基本搜索配置
```typescript
showSearch: true,
treeNodeFilterProp: 'TEXTMEMBER',
```

#### 自定义搜索过滤
```typescript
filterTreeNode: (inputValue: string, treeNode: any) => {
  if (!inputValue) return true;
  
  // 多字段搜索
  const searchFields = [
    treeNode.TEXTMEMBER,
    treeNode.NAMEMEMBER,
    treeNode.label,
  ];
  
  return searchFields.some(field => 
    field && field.toLowerCase().includes(inputValue.toLowerCase())
  );
},
```

## 🎯 最佳实践

### 1. 性能优化
- 使用 `immediate: true` 立即加载数据
- 实现懒加载减少初始数据量
- 使用 `treeDefaultExpandAll: false` 避免展开所有节点

### 2. 用户体验
- 提供清晰的 `placeholder` 提示
- 启用 `allowClear` 允许清除选择
- 使用 `loading` 状态显示加载过程

### 3. 错误处理
- 在API调用中添加 try-catch
- 提供用户友好的错误提示
- 设置合理的超时时间

### 4. 数据一致性
- 确保字段映射正确
- 验证父子关系的完整性
- 处理空数据和异常情况

## 📁 相关文件

- `select-receive-date-location.vue` - 主实现文件
- `treeselect-simple-config.vue` - 简化配置示例
- `TREESELECT-CONFIGURATION.md` - 详细配置说明

这个配置方案提供了完整的TreeSelect功能，支持复杂的数据结构和用户交互需求。
