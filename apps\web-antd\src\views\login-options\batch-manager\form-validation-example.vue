<template>
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">表单验证示例</h2>
    
    <!-- 按钮组 -->
    <div class="mb-4 space-x-2">
      <Button type="primary" @click="validateSpecificForm('1')">
        验证原辅包表单 (Key: 1)
      </Button>
      <Button type="primary" @click="validateSpecificForm('2')">
        验证常规样表单 (Key: 2)
      </Button>
      <Button type="primary" @click="validateSpecificForm('3')">
        验证免检表单 (Key: 3)
      </Button>
      <Button type="default" @click="validateCurrentActiveForm">
        验证当前激活表单
      </Button>
      <Button type="dashed" @click="validateAllFormsAtOnce">
        验证所有表单
      </Button>
    </div>

    <!-- 结果显示 -->
    <div v-if="validationResult" class="mb-4 p-4 border rounded">
      <h3 class="font-semibold mb-2">验证结果:</h3>
      <pre class="bg-gray-100 p-2 rounded text-sm">{{ JSON.stringify(validationResult, null, 2) }}</pre>
    </div>

    <!-- 表单组件 -->
    <PromptForProductStep ref="formStepRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Button, message } from 'ant-design-vue';
import PromptForProductStep from './prompt-for-product-step.vue';

const formStepRef = ref();
const validationResult = ref(null);

/**
 * 验证指定Key的表单
 */
const validateSpecificForm = async (key: string) => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.validateFormByKey(key);
    validationResult.value = result;
    
    if (result.success) {
      message.success(`表单 ${key} 验证成功`);
    } else {
      message.error(`表单 ${key} 验证失败: ${result.error}`);
    }
  } catch (error) {
    message.error(`验证过程出错: ${(error as Error).message}`);
  }
};

/**
 * 验证当前激活的表单
 */
const validateCurrentActiveForm = async () => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.validateCurrentForm();
    validationResult.value = result;
    
    if (result.success) {
      message.success('当前表单验证成功');
    } else {
      message.error(`当前表单验证失败: ${result.error}`);
    }
  } catch (error) {
    message.error(`验证过程出错: ${(error as Error).message}`);
  }
};

/**
 * 验证所有表单
 */
const validateAllFormsAtOnce = async () => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.validateAllForms();
    validationResult.value = result;
    
    if (result.success) {
      message.success('所有表单验证成功');
    } else {
      const failedForms = result.results.filter((r: any) => !r.success);
      message.error(`${failedForms.length} 个表单验证失败`);
    }
  } catch (error) {
    message.error(`验证过程出错: ${(error as Error).message}`);
  }
};
</script>
