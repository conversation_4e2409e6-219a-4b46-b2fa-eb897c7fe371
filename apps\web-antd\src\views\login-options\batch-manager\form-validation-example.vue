<template>
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">表单验证示例</h2>
    
    <!-- 按钮组 -->
    <div class="mb-4 space-x-2">
      <div class="mb-2">
        <span class="font-semibold">验证功能：</span>
        <Button type="primary" @click="validateSpecificForm('1')">
          验证原辅包表单 (Key: 1)
        </Button>
        <Button type="primary" @click="validateSpecificForm('2')">
          验证常规样表单 (Key: 2)
        </Button>
        <Button type="primary" @click="validateSpecificForm('3')">
          验证免检表单 (Key: 3)
        </Button>
        <Button type="default" @click="validateCurrentActiveForm">
          验证当前激活表单
        </Button>
        <Button type="dashed" @click="validateAllFormsAtOnce">
          验证所有表单
        </Button>
      </div>

      <div class="mb-2">
        <span class="font-semibold">提交功能：</span>
        <Button type="primary" danger @click="submitSpecificForm('1')">
          提交原辅包表单 (Key: 1)
        </Button>
        <Button type="primary" danger @click="submitSpecificForm('2')">
          提交常规样表单 (Key: 2)
        </Button>
        <Button type="primary" danger @click="submitSpecificForm('3')">
          提交免检表单 (Key: 3)
        </Button>
        <Button type="default" danger @click="submitCurrentActiveForm">
          提交当前激活表单
        </Button>
      </div>
    </div>

    <!-- 结果显示 -->
    <div v-if="validationResult" class="mb-4 p-4 border rounded">
      <h3 class="font-semibold mb-2">验证结果:</h3>
      <pre class="bg-gray-100 p-2 rounded text-sm">{{ JSON.stringify(validationResult, null, 2) }}</pre>
    </div>

    <!-- 表单组件 -->
    <PromptForProductStep ref="formStepRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Button, message } from 'ant-design-vue';
import PromptForProductStep from './prompt-for-product-step.vue';

const formStepRef = ref();
const validationResult = ref(null);

/**
 * 验证指定Key的表单
 */
const validateSpecificForm = async (key: string) => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.validateFormByKey(key);
    validationResult.value = result;

    if (result.success) {
      message.success(`表单 ${key} 验证成功`);
    } else {
      // 显示详细的验证错误信息
      message.error({
        content: result.error || `表单 ${key} 验证失败`,
        duration: 5,
      });

      // 如果有具体的字段错误，也可以在控制台显示
      if (result.validationResult?.errors) {
        console.error(`表单 ${key} 字段错误:`, result.validationResult.errors);
      }
    }
  } catch (error) {
    message.error(`验证过程出错: ${(error as Error).message}`);
  }
};

/**
 * 验证当前激活的表单
 */
const validateCurrentActiveForm = async () => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.validateCurrentForm();
    validationResult.value = result;
    
    if (result.success) {
      message.success('当前表单验证成功');
    } else {
      message.error(`当前表单验证失败: ${result.error}`);
    }
  } catch (error) {
    message.error(`验证过程出错: ${(error as Error).message}`);
  }
};

/**
 * 验证所有表单
 */
const validateAllFormsAtOnce = async () => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.validateAllForms();
    validationResult.value = result;

    if (result.success) {
      message.success('所有表单验证成功');
    } else {
      const failedForms = result.results.filter((r: any) => !r.success);
      message.error(`${failedForms.length} 个表单验证失败`);
    }
  } catch (error) {
    message.error(`验证过程出错: ${(error as Error).message}`);
  }
};

/**
 * 提交指定Key的表单
 */
const submitSpecificForm = async (key: string) => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.submitFormByKey(key);
    validationResult.value = result;

    if (result.success) {
      message.success(`表单 ${key} 提交成功`);
    } else {
      message.error({
        content: result.error || `表单 ${key} 提交失败`,
        duration: 5,
      });
    }
  } catch (error) {
    message.error(`提交过程出错: ${(error as Error).message}`);
  }
};

/**
 * 提交当前激活的表单
 */
const submitCurrentActiveForm = async () => {
  if (!formStepRef.value) {
    message.error('表单组件未加载');
    return;
  }

  try {
    const result = await formStepRef.value.submitCurrentForm();
    validationResult.value = result;

    if (result.success) {
      message.success('当前表单提交成功');
    } else {
      message.error({
        content: result.error || '当前表单提交失败',
        duration: 5,
      });
    }
  } catch (error) {
    message.error(`提交过程出错: ${(error as Error).message}`);
  }
};
</script>
