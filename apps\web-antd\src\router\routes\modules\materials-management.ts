import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: $t('materials-management.title'),
    },
    name: 'materials-management',
    path: '/materials-management',
    children: [
      {
        meta: {
          title: $t('materials-management.material-manager.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'material-manager',
        path: '/materials-management/material-manager',
        component: () =>
          import(
            '#/views/materials-management/material-manager/material-manager.vue'
          ),
      },
      // {
      //   meta: {
      //     title: $t('materials-management.location-management.title'),
      //     icon: 'ic:baseline-view-in-ar',
      //   },
      //   name: 'location-management',
      //   path: '/materials-management/location-management',
      //   component: () =>
      //     import(
      //       '#/views/materials-management/location-management/location-management.vue'
      //     ),
      // },
      {
        meta: {
          title: $t('materials-management.location-manage.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'location-management',
        path: '/materials-management/location-management',
        component: () =>
          import(
            '#/views/materials-management/location-management/location-management.vue'
          ),
      },
    ],
  },
];
export default routes;
