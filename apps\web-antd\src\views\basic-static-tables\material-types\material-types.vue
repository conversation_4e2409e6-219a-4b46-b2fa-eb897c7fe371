<script lang="ts" setup>
import type { VxeColumnPropTypes } from 'vxe-table';

import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { MaterialTypesApi } from '#/api/basic-static-tables/material-types';

import { onMounted, reactive, ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import {
  Button,
  Checkbox,
  message,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteChildType,
  deleteMatType,
  getChildTypeList,
  GetELNTemplate,
  getMatTypeDetailList,
  getMatTypeList,
  updateDept,
} from '#/api/basic-static-tables/material-types';
import { getLookupValuesSimple } from '#/api/common';

import AddChildTypeModal from './add-childtype.vue';
import AddMatTypeModal from './add-mattype.vue';
import AddAppearanceInspectionModal from './appearance-inspection.vue';
import { childTypeColumns, mattypeColumns, mattypeDetailColumns } from './data';

// const detailData = ref();
// const childData = ref();
const sMatType = ref<string>('');

const matTypeGridOptions: VxeTableGridOptions<MaterialTypesApi.MaterialTypes> =
  {
    columns: mattypeColumns(),
    stripe: true,
    border: true,
    keepSource: true,
    checkboxConfig: {
      highlight: true,
      range: true,
      labelField: 'select',
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    proxyConfig: {
      ajax: {
        query: async () => {
          return await getMatTypeList('N');
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };

const matTypeGridEvents: VxeGridListeners<MaterialTypesApi.MaterialTypes> = {
  currentRowChange: async ({ row }) => {
    if (row) {
      sMatType.value = row.MATTYPE;
      // 详细资料
      await loadDetailData();

      // 子类型
      await loadChildData();
    }
  },
};

async function loadDetailData() {
  const resDetail = await getMatTypeDetailList(sMatType.value);
  const data = Array.isArray(resDetail) ? resDetail : resDetail.items || [];
  if (Array.isArray(data)) {
    detailGridApi.setGridOptions({
      data,
    });
  }
}

async function loadChildData() {
  const resChildType = await getChildTypeList(sMatType.value);
  const data = Array.isArray(resChildType)
    ? resChildType
    : resChildType.items || [];
  if (Array.isArray(data)) {
    childTypeGridApi.setGridOptions({
      data,
    });
  }
}

const [MatTypeGrid, matTypeGridApi] = useVbenVxeGrid({
  gridOptions: matTypeGridOptions,
  gridEvents: matTypeGridEvents, // 确保事件被绑定
});

// 添加材料类型
const [MatTypeFormModal, matTypeModalApi] = useVbenModal({
  connectedComponent: AddMatTypeModal,
});

function addMatTypeFun() {
  matTypeModalApi.setData(null).open();
}

// 废弃材料类型
async function deleteMatTypeConfirm() {
  // 获取选中行
  const aMatType: string[] = matTypeGridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.MATTYPE);
  if (aMatType.length === 0) {
    message.warning('请先选择要移除的数据');
    return;
  }

  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除选中的 ${aMatType.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });
    await deleteMatType(aMatType);

    // 清空详情数据
    // detailData.value = [];
    // childData.value = [];

    message.success('移除成功');
    // 刷新
    onMatTypeRefresh();
  } catch (error) {
    if (error) {
      // message.error(`废弃失败：${(error as Error).message}`);
    }
  }
}

function onMatTypeRefresh() {
  matTypeGridApi.query();
}

// 更新实验室
async function updateDeptFun() {
  try {
    await confirm({
      title: '确认更新',
      content: `确定要更新实验室数据？`,
      icon: 'warning',
      centered: false,
    });

    await updateDept();
    message.success('更新成功');
    onMatTypeRefresh();
  } catch (error) {
    if (error) {
      // message.error(`废弃失败：${(error as Error).message}`);
    }
  }
}

function hasMatTypeEditStatus(row: MaterialTypesApi.MaterialTypes) {
  return matTypeGridApi.grid?.isEditByRow(row);
}

function editMatTypeRowEvent(row: MaterialTypesApi.MaterialTypes) {
  matTypeGridApi.grid?.setEditRow(row);
}

async function saveMatTypeRowEvent() {
  await matTypeGridApi.grid?.clearEdit();
  // addMatType(row);
  matTypeGridApi.setLoading(true);
  setTimeout(() => {
    matTypeGridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: AddAppearanceInspectionModal,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function viewDetails(row: MaterialTypesApi.MaterialTypes) {
  formDrawerApi.setData(row).open();
}

const cancelMatTypeRowEvent = (row: MaterialTypesApi.MaterialTypes) => {
  matTypeGridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    matTypeGridApi.grid.revertData(row);
  });
};

function createEditRender(): VxeColumnPropTypes.EditRender {
  return {
    name: 'select',
    options: [],
  };
}

const batchcrysReportIdEditRender = reactive(createEditRender());
const requestFormCryIdEditRender = reactive(createEditRender());
const labelSampleEditRender = reactive(createEditRender());
const sampleElnIdEditRender = reactive(createEditRender());
const sigModeEditRender = reactive(createEditRender());

onMounted(async () => {
  // 绑定COA模板数据
  const batchcrysReportIdResult = await getLookupValuesSimple(
    'BatchesCOACrystalReport',
  );
  batchcrysReportIdEditRender.options = batchcrysReportIdResult.items.map(
    (item: { TEXT: string; VALUE: string }) => ({
      label: item.TEXT,
      value: item.VALUE,
    }),
  );

  // 绑定请验单模板数据
  const requestFormCryIdResult = await getLookupValuesSimple(
    'BatchesRequestFormTemplate',
  );
  requestFormCryIdEditRender.options = requestFormCryIdResult.items.map(
    (item: { TEXT: string; VALUE: string }) => ({
      label: item.TEXT,
      value: item.VALUE,
    }),
  );

  // 绑定标签模板数据
  const labelSampleResult = await getLookupValuesSimple(
    'BatchesMaterialLabTemplate',
  );
  labelSampleEditRender.options = labelSampleResult.items.map(
    (item: { TEXT: string; VALUE: string }) => ({
      label: item.TEXT,
      value: item.VALUE,
    }),
  );

  // 绑定取样证模板数据
  const sampleElnIdResult = await GetELNTemplate('取样记录');
  sampleElnIdEditRender.options = sampleElnIdResult.items.map(
    (item: { TEXT: string; VALUE: string }) => ({
      label: item.TEXT,
      value: item.VALUE,
    }),
  );

  // 绑定签名数据
  const sigModeResult = await getLookupValuesSimple('MatInventorySigMode');
  sigModeEditRender.options = sigModeResult.items.map(
    (item: { TEXT: string; VALUE: string }) => ({
      label: item.TEXT,
      value: item.VALUE,
    }),
  );
});

// 材料类型详情
const matTypeDetailGridOptions: VxeTableGridOptions<MaterialTypesApi.MaterialTypesDetail> =
  {
    columns: mattypeDetailColumns(
      batchcrysReportIdEditRender,
      requestFormCryIdEditRender,
      labelSampleEditRender,
      sampleElnIdEditRender,
      sigModeEditRender,
    ),
    stripe: true,
    keepSource: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    // data: detailData.value,
    proxyConfig: {
      ajax: {
        query: async () => {
          if (sMatType.value === '') {
            return [];
          }
          return await getMatTypeDetailList(sMatType.value);
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };

const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: matTypeDetailGridOptions,
});

function hasMatTypeDetailEditStatus(row: MaterialTypesApi.MaterialTypesDetail) {
  return detailGridApi.grid?.isEditByRow(row);
}
function editMatTypeDetailRowEvent(row: MaterialTypesApi.MaterialTypesDetail) {
  detailGridApi.grid?.setEditRow(row);
}

async function saveMatTypeDetailRowEvent() {
  await detailGridApi.grid?.clearEdit();

  detailGridApi.setLoading(true);
  setTimeout(() => {
    detailGridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelMatTypeDetailRowEvent = (
  row: MaterialTypesApi.MaterialTypesDetail,
) => {
  detailGridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    detailGridApi.grid.revertData(row);
  });
};

// 子类型
const childTypeGridOptions: VxeTableGridOptions<MaterialTypesApi.ChildType> = {
  columns: childTypeColumns(),
  stripe: true,
  keepSource: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  // data: childlData.value,
  proxyConfig: {
    ajax: {
      query: async () => {
        if (sMatType.value === '') {
          return [];
        }
        return await getChildTypeList(sMatType.value);
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'ORIGREC',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [ChildTypeGrid, childTypeGridApi] = useVbenVxeGrid({
  gridOptions: childTypeGridOptions,
});

// 添加子单位类型
const [ChildFormModal, childTypeModalApi] = useVbenModal({
  connectedComponent: AddChildTypeModal,
});

function addChildTypeForm() {
  childTypeModalApi.setData({ MATTYPE: sMatType.value }).open();
}

// 删除子单位类型
async function deleteChildTypeConfirm() {
  // 获取选中行
  const aOrigrec: number[] = childTypeGridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);
  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteChildType(aOrigrec);

    message.success('删除成功');
    onChildTypeRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

async function onChildTypeRefresh() {
  await loadChildData();
}

function hasChildTypeEditStatus(row: MaterialTypesApi.ChildType) {
  return childTypeGridApi.grid?.isEditByRow(row);
}

function editChildTypeRowEvent(row: MaterialTypesApi.ChildType) {
  childTypeGridApi.grid?.setEditRow(row);
}

async function saveChildTypeRowEvent() {
  await childTypeGridApi.grid?.clearEdit();
  // updateChildType(row);
  childTypeGridApi.setLoading(true);
  setTimeout(() => {
    childTypeGridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelChildTypeRowEvent = (row: MaterialTypesApi.ChildType) => {
  childTypeGridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    childTypeGridApi.grid.revertData(row);
  });
};

const activeKey = ref('1');

const checked = ref(false);
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onMatTypeRefresh" />
    <MatTypeFormModal @success="onMatTypeRefresh" />
    <AppearanceInspectionModal />
    <ChildFormModal @success="onChildTypeRefresh" />
    <div class="vp-raw h-[400px] w-full">
      <MatTypeGrid>
        <template #action="{ row }">
          <template v-if="hasMatTypeEditStatus(row)">
            <Button type="link" @click="saveMatTypeRowEvent()">
              {{ $t('basic-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelMatTypeRowEvent(row)">
              {{ $t('basic-static-tables.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editMatTypeRowEvent(row)">
              {{ $t('basic-static-tables.edit') }}
            </Button>
            <Button type="link" @click="viewDetails(row)">
              {{ $t('basic-static-tables.detail') }}
            </Button>
          </template>
        </template>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="addMatTypeFun">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="deleteMatTypeConfirm">
              {{ $t('basic-static-tables.removed') }}
            </Button>
            <!--             <Button @click="appearanceInspectionForm">
              {{
                $t('basic-static-tables.material-types.appearanceinspection')
              }}
            </Button> -->
            <Button @click="updateDeptFun">
              {{ $t('basic-static-tables.material-types.updatedept') }}
            </Button>
            <Checkbox v-model:checked="checked">
              {{ $t('basic-static-tables.material-types.showall') }}
            </Checkbox>
          </Space>
        </template>
      </MatTypeGrid>
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane key="1" tab="详细设置">
        <div class="vp-raw h-[400px] w-full">
          <DetailGrid>
            <template #action="{ row }">
              <template v-if="hasMatTypeDetailEditStatus(row)">
                <Button type="link" @click="saveMatTypeDetailRowEvent()">
                  {{ $t('basic-static-tables.save') }}
                </Button>
                <Button type="link" @click="cancelMatTypeDetailRowEvent(row)">
                  {{ $t('basic-static-tables.cancel') }}
                </Button>
              </template>
              <template v-else>
                <Button type="link" @click="editMatTypeDetailRowEvent(row)">
                  {{ $t('basic-static-tables.edit') }}
                </Button>
              </template>
            </template>
          </DetailGrid>
        </div>
      </TabPane>
      <TabPane key="2" tab="子类型" style="height: 100%" force-render>
        <div class="vp-raw h-[400px] w-full">
          <ChildTypeGrid>
            <template #action="{ row }">
              <template v-if="hasChildTypeEditStatus(row)">
                <Button type="link" @click="saveChildTypeRowEvent()">
                  {{ $t('basic-static-tables.save') }}
                </Button>
                <Button type="link" @click="cancelChildTypeRowEvent(row)">
                  {{ $t('basic-static-tables.cancel') }}
                </Button>
              </template>
              <template v-else>
                <Button type="link" @click="editChildTypeRowEvent(row)">
                  {{ $t('basic-static-tables.edit') }}
                </Button>
              </template>
            </template>
            <template #toolbar-actions>
              <Space>
                <Button type="primary" @click="addChildTypeForm">
                  {{ $t('ui.actionTitle.create') }}
                </Button>
                <Button type="primary" danger @click="deleteChildTypeConfirm">
                  {{ $t('ui.actionTitle.delete') }}
                </Button>
              </Space>
            </template>
          </ChildTypeGrid>
        </div>
      </TabPane>
    </Tabs>
  </Page>
</template>
