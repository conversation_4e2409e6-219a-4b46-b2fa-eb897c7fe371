---
description: 
globs: 
alwaysApply: false
---
# Vue Vben Admin 开发与构建指南

## 开发环境准备

### 前置要求
- Vue 3
- TypeScript
- Vite
- Vue Router
- Pnpm
- Turbo
- Tailwind CSS

### IDE 配置
推荐使用 VSCode，安装以下插件：
- Vue - Official - Vue 官方插件（必备）
- Tailwind CSS - Tailwindcss 提示插件
- CSS Variable Autocomplete - CSS 变量提示
- Iconify IntelliSense - Iconify 图标插件
- i18n Ally - i18n 插件
- ESLint - 脚本代码检查
- Prettier - 代码格式化
- Stylelint - CSS 格式化
- Code Spell Checker - 单词拼写检查
- DotENV - .env 文件高亮

## 开发规范

### 目录规范

1. 应用开发 (`apps/`)
```bash
apps/web-antd/
├── src/
│   ├── main.ts           # 入口文件
│   ├── App.vue          # 根组件
│   ├── router/          # 路由配置
│   ├── store/           # 状态管理
│   └── views/           # 页面组件
```

2. 包开发 (`packages/`)
```bash
packages/
├── @core/              # 核心功能包
├── effects/           # 副作用包
└── utils/            # 工具函数包
```

3. 组件开发
```bash
ComponentName/
├── index.ts          # 入口和类型
├── src/
│   ├── ComponentName.vue  # 组件主文件
│   ├── props.ts     # props 定义
│   └── types.ts     # 类型定义
└── style/
    └── index.less   # 组件样式
```

### 代码规范

1. 命名规范
- 组件名：PascalCase
- 文件名：kebab-case
- 变量名：camelCase
- 常量名：UPPER_CASE

2. 样式规范
- 使用 Less 预处理器
- 遵循 BEM 命名规范
- 优先使用 Tailwind CSS

3. Git 提交规范
```bash
# 使用 Commitizen
pnpm commit
```

## 构建与部署

### 开发环境

1. 启动开发服务
```bash
# 启动所有项目
pnpm dev

# 启动特定项目
pnpm dev:antd    # Ant Design Vue 版本
pnpm dev:naive   # Naive UI 版本
pnpm dev:ele     # Element Plus 版本
```

2. 开发工具
```bash
# 开启 Vue DevTools
# .env.development
VITE_DEVTOOLS=true
```

### 构建命令

1. 基础构建
```bash
# 构建所有项目
pnpm build

# 构建并分析
pnpm build:analyze
```

2. 单项目构建
```bash
# Ant Design Vue 版本
pnpm build:antd

# Naive UI 版本
pnpm build:naive

# Element Plus 版本
pnpm build:ele
```

3. 多环境构建
```bash
# 生产环境
pnpm build:prod

# 测试环境
pnpm build:test

# 分析环境
pnpm build:analyze
```

### 部署流程

1. 环境配置
- `.env.development` - 开发环境
- `.env.production` - 生产环境
- `.env.test` - 测试环境

2. Docker 部署
```bash
# 构建 Docker 镜像
pnpm build:docker
```

3. 预览
```bash
# 预览构建结果
pnpm preview
```

### 质量检查

1. 类型检查
```bash
pnpm check:type
```

2. 依赖检查
```bash
# 检查循环依赖
pnpm check:circular

# 检查依赖问题
pnpm check:dep
```

3. 代码质量
```bash
# ESLint 检查
pnpm lint

# 样式检查
pnpm lint:stylelint
```

## 静态资源管理

### 公共资源
- 存放位置：`public/static/`
- 引用方式：`src="/static/xxx.png"`

### 组件资源
- 组件相关资源放在组件目录下
- 使用相对路径引用

## 依赖管理

### 安装依赖
```bash
# 安装依赖
pnpm install

# 重新安装依赖
pnpm reinstall
```

### 更新依赖
```bash
# 更新所有依赖
pnpm update:deps
```

## 版本发布

### Changeset
```bash
# 创建变更集
pnpm changeset

# 更新版本
pnpm version
```
