<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addInspection,
  getMatInfo,
  getMatInfoList,
  getSuppliersData,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formRef = ref();
async function onAdHocSubmit(values: Record<string, any>) {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = values as BatcheManagerApi.Batches;
    await addInspection(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '操作成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `操作失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}

const [ImspectionForm, inspectionFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onAdHocSubmit,
  layout: 'vertical',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.batchManager.batchno'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'MATCODE',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getMatInfoList.bind(null, '', '', ''),
        labelField: 'MATCODE',
        valueField: 'MATCODE',
        onChange: async (value: string) => {
          const matinfo = await getMatInfo(value);
          if (matinfo.length > 0) {
            const matName = matinfo[0];
            inspectionFormApi.setFieldValue('MATNAME', matName);

            const resMfg = await getSuppliersData(value, 'SC');
            const mfgOptions = resMfg.items.map(
              (item: { TEXT: string; Value: string }) => ({
                label: item.TEXT,
                value: item.Value,
              }),
            );
            const resSupp = await getSuppliersData(value, 'GY');
            const suppOptions = resSupp.items.map(
              (item: { TEXT: string; Value: string }) => ({
                label: item.TEXT,
                value: item.Value,
              }),
            );
            inspectionFormApi.updateSchema([
              {
                fieldName: 'MFG',
                componentProps: {
                  options: mfgOptions,
                },
              },
              {
                fieldName: 'SUPPCODE',
                componentProps: {
                  options: suppOptions,
                },
              },
            ]);
          }
        },
      },
      label: $t('login-options.batchManager.matcode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'MATNAME',
      label: $t('login-options.batchManager.matname'),
      rules: 'required',
    },
    {
      component: 'DatePicker',
      fieldName: 'PRODUCTION_DATE',
      label: $t('login-options.batchManager.productionDate'),
    },
    {
      component: 'Input',
      fieldName: 'MATNO',
      label: $t('login-options.batchManager.matNo'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'MFG',
      label: $t('login-options.batchManager.mfg'),
    },
    {
      component: 'Input',
      fieldName: 'SAMPLE_VOL',
      label: $t('login-options.batchManager.sampleVol'),
    },
    {
      component: 'Input',
      fieldName: 'SAMPLE_UNITS',
      label: $t('login-options.batchManager.sampleUnits'),
    },
    {
      component: 'DatePicker',
      fieldName: 'EXPDATE',
      label: $t('login-options.batchManager.expdate'),
    },
    {
      component: 'ApiSelect',
      fieldName: 'SUPPCODE',
      label: $t('login-options.batchManager.suppnam'),
    },
    {
      component: 'Input',
      fieldName: 'PACKING_SPEC',
      label: $t('login-options.batchManager.packingSpec'),
    },
    {
      component: 'Input',
      fieldName: 'COMMENTS',
      label: $t('login-options.batchManager.comments'),
    },
  ],
  // wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-8',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  onConfirm: async () => {
    const st = await inspectionFormApi.validateAndSubmitForm();
    console.log('st', st);
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // await RawMaterialForm.resetFields();
    }
  },
});
</script>

<template>
  <Modal title="免检登录" class="h-[1000px] w-[1200px]">
    <Page>
      <ImspectionForm ref="formRef" />
    </Page>
  </Modal>
</template>
