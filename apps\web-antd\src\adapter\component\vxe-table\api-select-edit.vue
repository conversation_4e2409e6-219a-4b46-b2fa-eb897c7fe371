<script lang="ts" setup>
import type { VxeGlobalRendererHandles, VxeTableDefines } from 'vxe-table';

import type { PropType } from 'vue';

import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { Select } from 'ant-design-vue';

const props = defineProps({
  renderParams: {
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableEditParams>,
    default: () => ({}),
  },
  api: {
    type: Function as PropType<() => Promise<Recordable<any>[]>>,
    default: () => Promise.resolve([]),
  },
  labelField: {
    type: String,
    default: 'label',
  },
  valueField: {
    type: String,
    default: 'value',
  },
});

const selectOptions = ref<Recordable<any>[]>([]);

const currColumn = ref<VxeTableDefines.ColumnInfo>();
const currRow = ref();
const labelField = ref<string>(props.labelField);
const valueField = ref<string>(props.valueField);

const load = () => {
  const { renderParams, api } = props;
  const { row, column } = renderParams;
  currRow.value = row;
  currColumn.value = column;
  api().then((data) => {
    selectOptions.value = data;
  });
};

load();
</script>
<template>
  <div v-if="currRow && currColumn" class="edit-down-table">
    <Select
      :options="selectOptions"
      :field-names="{ label: labelField, value: valueField }"
      v-model:value="currRow[currColumn.field]"
    />
  </div>
</template>
