<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue';

import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  Button,
  message,
  Modal,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteEquipmentApi,
  getEquipmentListApi,
  getReportPrintApi,
} from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';
import { useEquipmentStore } from '#/store';

import BaseInfo from './base-info.vue';
import CommonModel from './components/common-modal.vue';
import DataCollect from './data-collect.vue';
import DeviceDoc from './device-doc.vue';
import { useColumns } from './equipment-mg-data';
import EventRecord from './event-record.vue';
import UseRecord from './use-record.vue';

const equipmentStore = useEquipmentStore();

onMounted(async () => {
  const data = await getReportPrintApi();
  console.warn(data);
});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: CommonModel,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: useColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = await getEquipmentListApi();
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const currentRow = ref<EquipmentMgApi.EquipmentData | null>(null);
const gridEvents: VxeGridListeners<EquipmentMgApi.MetaDataEquipment> = {
  currentChange: async ({ row }) => {
    if (row) {
      equipmentStore.setCurrentRow(row);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});

const value = ref('设备铭牌');
const options = ref<SelectProps['options']>([
  {
    value: '设备铭牌',
    label: '设备铭牌',
  },
  {
    value: '设备使用日志',
    label: '设备使用日志',
  },
]);
const activeKey = ref('基本信息');
const tabList = [
  {
    title: '基本信息',
    page: BaseInfo,
  },
  {
    title: '事件记录',
    page: EventRecord,
  },
  {
    title: '数据采集',
    page: DataCollect,
  },
  {
    title: '设备文档',
    page: DeviceDoc,
  },
  {
    title: '使用记录',
    page: UseRecord,
  },
];
const onDelete = () => {
  const checkLookupNames: string[] = gridApi.grid.getCheckboxRecords();

  if (checkLookupNames.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  if (checkLookupNames.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }
  Modal.confirm({
    title: '询问',
    content: '确实要删除选定的命令吗？',
    cancelText: '否',
    okText: '是',
    async onOk() {
      await deleteEquipmentApi(checkLookupNames[0]);
    },
    onCancel() {},
  });
};
const onAdd = () => {
  formModalApi.setData(null).open();
};
const selectReport = () => {};
const onRefresh = () => {
  gridApi.query();
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <div class="flex h-full flex-row">
      <Grid>
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
            <Button type="primary" @click="onAdd">
              {{ $t('equipment.equipment-mg.add') }}
            </Button>
            <Button type="primary" @click="selectReport">
              {{ $t('equipment.equipment-mg.selectReport') }}
            </Button>
            <Select v-model:value="value" style="width: 120px">
              <SelectOption
                v-for="item in options"
                :value="item.value"
                :key="item.value"
              >
                {{ item.label }}
              </SelectOption>
            </Select>
          </Space>
        </template>
      </Grid>
      <div class="flex-1 bg-white px-10">
        <Tabs v-model:active-key="activeKey">
          <TabPane v-for="item in tabList" :key="item.title" :tab="item.title">
            <component
              :is="
                tabList.find((item) => item.title === activeKey)?.page ||
                BaseInfo
              "
              :current-row="currentRow"
            />
          </TabPane>
        </Tabs>
      </div>
    </div>
  </Page>
</template>
