import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: $t('equipment.title'),
    },
    name: 'Equipment',
    path: '/equipment',
    children: [
      {
        meta: {
          title: $t('equipment.equipment-mg.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'equipment-mg',
        path: '/equipment/equipment-mg',
        component: () =>
          import('#/views/equipment/equipment-mg/equipment-mg.vue'),
      },
      {
        meta: {
          title: $t('equipment.main-form.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'main-form',
        path: '/equipment/main-form',
        component: () => import('#/views/equipment/main-form/main-form.vue'),
      },
      {
        meta: {
          title: $t('equipment.course-form.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'course-form',
        path: '/equipment/course-form',
        component: () =>
          import('#/views/equipment/course-form/course-form.vue'),
      },
      {
        meta: {
          title: $t('equipment.course-schedules.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'course-schedules',
        path: '/equipment/course-schedules',
        component: () =>
          import('#/views/equipment/course-schedules/course-schedules.vue'),
      },
    ],
  },
];
export default routes;
