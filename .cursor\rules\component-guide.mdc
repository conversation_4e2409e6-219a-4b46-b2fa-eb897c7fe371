---
description:
globs:
alwaysApply: false
---
# 组件开发指南

## 组件结构

每个组件应该包含以下文件：
```
ComponentName/
  ├── index.tsx        # 组件入口
  ├── style.less       # 样式文件
  ├── __tests__/       # 测试文件
  └── README.md        # 组件文档
```

## 组件开发规范

1. 使用 TypeScript 开发组件
2. 使用函数式组件和 Hooks
3. 使用 Props 类型定义
4. 编写单元测试
5. 添加组件文档

## 组件示例

```tsx
import React from 'react';
import { ButtonProps } from './types';
import './style.less';

export const Button: React.FC<ButtonProps> = ({
  children,
  type = 'primary',
  ...props
}) => {
  return (
    <button className={`btn btn-${type}`} {...props}>
      {children}
    </button>
  );
};
```

## 组件测试

```tsx
import { render, screen } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
});
```

## 组件文档

组件文档应包含：
- 组件描述
- Props 说明
- 使用示例
- 注意事项
