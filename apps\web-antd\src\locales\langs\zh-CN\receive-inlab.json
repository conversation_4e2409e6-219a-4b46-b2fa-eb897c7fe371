{"title": "样品接收", "save": "保存", "cancel": "取消", "edit": "编辑", "remove": "移除", "operation": "操作", "tat": "加急？", "sampleType": "样品类型", "flDispStatus": "流程状态", "receivedSts": "接收状态", "batchNo": "批号", "orderNo": "样品编号", "sampDate": "取样日期", "clsampleno": "样品描述", "retestBatchId": "复验原批ID", "matName": "检品名称", "sampleName": "样品名称", "sampledby": "取样人", "actualvol": "实际取样量", "status": "状态", "finishAliquots": "完成分样", "receive": "接收", "goback": "退回", "receiveByScanCode": "扫码接收", "otherSamplecantreturn": "当前批次下已经有其他的样品测试进入了结果录入之后的流程，不可退回！", "compdat": "接收日期", "locationcode": "存储位置", "ActiveNumIsNotNull": "实际取样量不能为空或小于0 ！", "aliquots": "分样", "left": "<", "right": ">", "text": "项目", "locationName": "存储位置"}