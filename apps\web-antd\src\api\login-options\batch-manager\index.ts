import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace BatcheManagerApi {
  export interface Batches {
    ORIGREC: number;
    FLDISPSTATUS: string;
    REPORTFLAG: string;
    TAT: string;
    NEED_REPORT: string;
    TYPE: string;
    OPENDATE: Date;
    DISPSTS: string;
    PLANT: string;
    PROCESS: string;
    BATCHID: number;
    BATCHNO: string;
    SUPPBATCHNO: string;
    PRODGROUP: string;
    SAMPLEGROUPNAME: string;
    MATCODE: string;
    BATCHNAME: string;
    COMMENTS: string;
    MFG: string;
    SUPPCODE: string;
    SUPPNAM: string;
    NUM_ARRIVAL: string;
    NUM_UNPACK: string;
    ESTIMATEDVOL: string;
    ACTUALVOL: string;
    ESTIMATEDVOL_UNITS: string;
    PACKING_SPEC: string;
    STORAGE_CONDITION: string;
    ORDERNO: string;
    VERSION_NO: string;
    PRODUCTGRADE: string;
    PRODUCTION_DATE: Date;
    EXPDATE: Date;
    RETEST_DATE: Date;
    FLSTATUS: string;
    IFLAG: string;
    PATH_RAWRECORD_RPT: string;
    DATE_RAWRECORD: Date;
    REQUEST_DATE: Date;
    REQUESTER: string;
    REQUEST_DEPT: string;
    RETEST_BATCHID: number;
    RETESTORDNO: string;
    REQUEST_REASON: string;
    REQUESTID: string;
    SAMPLE_VOL: string;
    SAMPLE_UNITS: string;
    SAMPDATE: Date;
    SAMPLEDBY: string;
    SAMPLEGROUPCODE: string;
    VESSEL: string;
    BUILDING_ID: number;
    ROOM_ID: number;
    MODE: string;
    SPCODELIST: string[][];
    MATNAME: string;
    SP_CODE: number;
    SAMPLEDESC: string;
    AdHocTests: string[];
    ENDDATE: Date;
    SKIPSAMP: string;
    SKIPREC: string;
    MATNO: string;
    RELEASE_QTY: number;
    RETEST_EXPIRY_DATE: Date;
    RELEASE_UNITS: string;
  }

  export interface BatchOrders {
    ORIGREC: number;
    FLDISPSTATUS: string;
    STATUS: string;
    ORDNO: string;
    SAMPLE_NAME: string;
    MATNAME: string;
    SPECIFICATION: string;
    DUEDAT: Date;
    MATCODE: string;
    SPECNO: number;
    FLSTATUS: string;
    CLSAMPNO: string;
    SAMPDESC: string;
    ELN_ID: string;
    ELN_ORIGREC: number;
    SP_CODE: number;
    PROFILE: string;
    NUM: number;
    BATCHID: number;
  }

  export interface BatchOrdtask {
    ORIGREC: number;
    PROFILE: string;
    ORDNO: string;
    OTTESTNO: string;
    SERVGRP: string;
    DEPT: string;
    TS: string;
    TESTNO: string;
    METHOD: string;
    VERSION: number;
    ANALYSISDUEDATE: Date;
    SPTESTSORTER: number;
    NEED_REPORT: string;
    REP: number;
    SINONYM: string;
    FINAL: string;
    S: string;
    CHARLIMITS: string;
    LOWA: string;
    LOWB: string;
    LOWC: string;
    HIGHC: string;
    HIGHB: string;
    HIGHA: string;
    PASS_TEXT: string;
    PASS_TEXT_ENG: string;
  }

  export interface BatchRecipe {
    ORIGREC: number;
    MATCODE: string;
    MATNAME: string;
    COMPONENTLOTNUMBER: string;
    SAMPLEGROUPNAME: string;
    QTY: string;
    UNITS: string;
    BATCHIDFROMBATCHES: string;
  }

  export interface BatchSamplingRequirement {
    ORIGREC: number;
    CLSAMPNO: string;
    ORDNO: string;
    INVENTORYID: number;
    EXTERNAL_ID: string;
    SAMPLE_TYPE: string;
    CONTAINERQTY: number;
    CONTAINER_UNITS: string;
    SAMPLINGPOSITION: string;
    FORLAB: string;
    SAMPLESIZE: string;
    NUMBEROFCONTAINERS: number;
    CONDITION: string;
    STATUS: string;
    SAMPLEGROUPCODE: string;
    BATCHID: number;
    CREATEINVENTORYID: string;
  }

  export interface BatchSamplingRequTests {
    ORIGREC: number;
    ORDNO: string;
    INVENTORYID: number;
    TESTCODE: number;
    TESTNO: string;
  }
}

async function getTestPlanGroups(sMatCode: string, typeOfGroup: string) {
  return await getDataSetNoPage('BatchManager.getTestPlanGroups', [
    sMatCode,
    typeOfGroup,
  ]);
}

async function cbMfgEquip(typeOfGroup: string) {
  return getDataSet('BatchManager.cbMfgEquip', [typeOfGroup]);
}

async function cbTemplateByMfgEquip(
  sPlant: string,
  typeOfGroup: string,
  sMatCode: string,
  sSupplierCode: string,
) {
  return getDataSet('BatchManager.cbTemplateByMfgEquip', [
    sPlant,
    typeOfGroup,
    sMatCode,
    sSupplierCode,
  ]);
}

async function getSampGroupsByProdGrpAndPlant(
  sTestPlanGrp: string,
  typeOfGroup: string,
  sPlant: string,
  sMatCode: string,
) {
  return getDataSet('BatchManager.getSampGroupsByProdGrpAndPlant', [
    sTestPlanGrp,
    typeOfGroup,
    sPlant,
    sMatCode,
  ]);
}

async function rawMatSpCodecb(
  sSampleGroupCode: string,
  sMatCode: string,
  sSupplierCode: string,
) {
  return await getDataSet('BatchManager.RawMatSp_Code_cb', [
    sSampleGroupCode,
    sMatCode,
    sSupplierCode,
  ]);
}

async function getMatInfo(sMatCode: string) {
  return await callServer('BatchManager.GetMatInfo', [sMatCode]);
}

async function getSuppliersData(sMatCode: string, sType: string) {
  return await getDataSet('BatchManager.GetSuppliersData', [sMatCode, sType]);
}

async function calculateNumUnPack(nRawNumArrival: string) {
  return await callServer('BatchManager.calculateNumUnPack', [nRawNumArrival]);
}

async function getMatInfoList(
  sMatType: string,
  sMatCode: string,
  sMatName: string,
) {
  return await getDataSetNoPage('BatchManager.GetMatInfo', [
    sMatType,
    sMatCode,
    sMatName,
  ]);
}

async function getRequestReason() {
  return getDataSet('GenericMetadata.LookupValuesSimple', ['RequestReason']);
}

async function getProductStepArray(sSampleGroupCode: string) {
  return await callServer('BatchManager.GetProductStepArray', [
    sSampleGroupCode,
  ]);
}

async function getBatchesList(Mode: string, sWhere: string, sStepCode: string) {
  return getDataSet('BatchManager.Batchesdg', [Mode, sWhere, sStepCode]);
}

async function cancel(sBatchId: number, sType: string) {
  return await callServer('BatchManager.Cancel', [sBatchId, sType]);
}

async function getSampleDgList(
  sBatchId: number,
  sMode: string,
  sQueryMode: string,
) {
  return getDataSet('BatchManager.Sample_dg', [sBatchId, sMode, sQueryMode]);
}

async function getMatrixSample(sSampleGroupCode: string, spCode: number) {
  return getDataSet('BatchManager.MatrixSamp_cb', [sSampleGroupCode, spCode]);
}

async function getSelectProfile(spCode: number) {
  return getDataSet('BatchManager.SelectProfile_cb', [spCode]);
}

async function copySampleAndAudit(
  sSpCode: number,
  sProfile: string,
  sBatchId: number,
  NumCopies: number,
  sOrdNo: string,
) {
  return await callServer('BatchManager.copySampleAndAudit', [
    sSpCode,
    sProfile,
    sBatchId,
    NumCopies,
    sOrdNo,
    'COPY',
  ]);
}

async function removedSample(sOrdNo: string) {
  return await callServer('BatchManager.removedSample', [sOrdNo]);
}

async function getSampleTestsAnalytesList(
  sOrdNo: string,
  sMode: string,
  sQueryMode: string,
) {
  return getDataSet('BatchManager.SampleTestsAnalytes', [
    sOrdNo,
    sMode,
    sQueryMode,
  ]);
}

async function getBatchRecipesList(sBatchId: number) {
  return getDataSet('BatchManager.FormulationDetails_dg', [sBatchId]);
}

async function deleteFormulationItem(sOrigrec: number) {
  return await callServer('BatchManager.deleteFormulationItem', [sOrigrec]);
}

async function getSampleRequirementList(sBatchId: number) {
  return getDataSet('BatchManager.SamplingRequiremnts_dg', [sBatchId]);
}

async function addAdHocSamplingRequirement(
  data: BatcheManagerApi.BatchSamplingRequirement,
) {
  return await callServer('BatchManager.AddAdHocSamplingRequirement', [
    data.SAMPLEGROUPCODE,
    data.BATCHID,
    null,
    data.SAMPLESIZE,
    data.NUMBEROFCONTAINERS,
    data.SAMPLINGPOSITION,
    data.FORLAB,
    data.CREATEINVENTORYID,
    data.CONTAINERQTY,
    data.CONTAINER_UNITS,
    data.CONDITION,
    null,
    '',
    true,
    null,
    null,
    false,
    data.SAMPLE_TYPE,
    'BATCH_MANAGER',
  ]);
}

async function removedAdHocSamplingRequirement(
  aOrigrec: number[],
  sComment: string,
  aOrdNo: string[],
) {
  return await callServer('BatchManager.removedAdHocSamplingRequirement', [
    aOrigrec,
    sComment,
    aOrdNo,
  ]);
}

async function getSampleReqTestsList(sOrdNo: string, nInventoryID: number) {
  return getDataSet('BatchManager.BatchSamplingReq_UsedByTests', [
    sOrdNo,
    nInventoryID,
  ]);
}

async function getSampleReqUsedTestsMc(sOrdNo: string, nInventoryID: number) {
  return getDataSetNoPage('BatchManager.BatchSamplingReq_UsedByTests_MC', [
    sOrdNo,
    nInventoryID,
  ]);
}

async function updateSampleReqUsedTestsMc(
  sOrdNo: string,
  nInventoryID: number,
  aTestCode: string[],
) {
  return await callServer('BatchManager.UpdateBatchSamplingReq_UsedByTests', [
    sOrdNo,
    nInventoryID,
    aTestCode,
  ]);
}

async function addRawMatSample(data: BatcheManagerApi.Batches) {
  return await callServer('BatchManager.AddRawMatSample', [
    data.MATCODE,
    data.MATNAME,
    data.MFG,
    null,
    null,
    null,
    data.COMMENTS,
    null,
    null,
    data.ACTUALVOL,
    data.PROCESS,
    data.PACKING_SPEC,
    data.REQUEST_DEPT,
    data.STORAGE_CONDITION,
    data.RETEST_BATCHID,
    data.SAMPLEGROUPCODE,
    data.SP_CODE,
    data.SUPPCODE,
    data.BATCHNO,
    data.PRODUCTION_DATE,
    data.REQUEST_DATE,
    null,
    data.ESTIMATEDVOL,
    data.ESTIMATEDVOL_UNITS,
    data.REQUESTER,
    0,
    data.SUPPBATCHNO,
    data.EXPDATE,
    null,
    data.PLANT,
    data.MODE,
    null,
    false,
    false,
    null,
    null,
    null,
    '跳批检验信息',
    data.RETEST_DATE,
    data.NUM_ARRIVAL,
    data.NUM_UNPACK,
    data.PRODUCTGRADE,
    data.VERSION_NO,
    data.REQUEST_REASON,
    data.RETESTORDNO,
  ]);
}
async function addLotPlanSamples(data: BatcheManagerApi.Batches) {
  return await callServer('BatchManager.AddLotPlanSamples', [
    data.REQUESTID,
    data.ORDERNO,
    data.IFLAG,
    data.PROCESS,
    data.PACKING_SPEC,
    data.ACTUALVOL,
    data.SAMPLE_VOL,
    data.SAMPLE_UNITS,
    data.COMMENTS,
    data.REQUEST_DATE,
    data.REQUESTER,
    data.SAMPDATE,
    data.SAMPLEDBY,
    data.PRODUCTION_DATE,
    data.REQUEST_DEPT,
    data.RETEST_BATCHID,
    data.SAMPLEGROUPCODE,
    data.SPCODELIST,
    data.BATCHNO,
    -1,
    data.OPENDATE,
    null,
    null,
    data.BATCHNAME,
    data.SUPPBATCHNO,
    data.VESSEL,
    data.ESTIMATEDVOL,
    data.ESTIMATEDVOL_UNITS,
    data.EXPDATE,
    data.BUILDING_ID,
    data.ROOM_ID,
    data.PLANT,
    data.MODE,
    null,
    false,
    false,
    null,
    null,
    null,
    data.STORAGE_CONDITION,
    data.REQUEST_REASON,
    data.RETESTORDNO,
  ]);
}

async function addAdHocSample(data: BatcheManagerApi.Batches) {
  return await callServer('BatchManager.AddAdHocSample', [
    data.BATCHNO,
    data.SAMPLEDESC,
    data.AdHocTests,
    null,
    data.PLANT,
    data.MODE,
    null,
    false,
    false,
    null,
    null,
    data.ENDDATE,
    data.PLANT,
    data.MATCODE,
    data.SKIPSAMP,
    data.SKIPREC,
    data.SAMPDATE,
    data.SAMPLEDBY,
    data.RETEST_BATCHID,
    data.RETESTORDNO,
  ]);
}

async function addInspection(data: BatcheManagerApi.Batches) {
  return await callServer('BatchManager.AddInspection', [
    data.BATCHNO,
    data.MATCODE,
    data.MATNAME,
    data.MATNO,
    data.SAMPLE_VOL,
    data.RELEASE_QTY,
    data.MFG,
    data.PRODUCTION_DATE,
    data.EXPDATE,
    data.RETEST_EXPIRY_DATE,
    data.SAMPLE_UNITS,
    data.RELEASE_UNITS,
    data.SUPPCODE,
    data.PACKING_SPEC,
    data.COMMENTS,
  ]);
}

async function StartBatch(sBatchId: number) {
  return await callServer('BatchManager.StartBatch', [sBatchId]);
}

async function StartBacthTwo(sBatchId: number) {
  return await callServer('BatchManager.StartBacthTwo', [sBatchId]);
}

async function convertLimsMatCode(aMatCode: string[]) {
  return await callServer('BatchManager.convertLimsMatCode', [aMatCode]);
}

export {
  addAdHocSample,
  addAdHocSamplingRequirement,
  addInspection,
  addLotPlanSamples,
  addRawMatSample,
  calculateNumUnPack,
  cancel,
  cbMfgEquip,
  cbTemplateByMfgEquip,
  convertLimsMatCode,
  copySampleAndAudit,
  deleteFormulationItem,
  getBatchesList,
  getBatchRecipesList,
  getMatInfo,
  getMatInfoList,
  getMatrixSample,
  getProductStepArray,
  getRequestReason,
  getSampGroupsByProdGrpAndPlant,
  getSampleDgList,
  getSampleReqTestsList,
  getSampleRequirementList,
  getSampleReqUsedTestsMc,
  getSampleTestsAnalytesList,
  getSelectProfile,
  getSuppliersData,
  getTestPlanGroups,
  rawMatSpCodecb,
  removedAdHocSamplingRequirement,
  removedSample,
  StartBacthTwo,
  StartBatch,
  updateSampleReqUsedTestsMc,
};
