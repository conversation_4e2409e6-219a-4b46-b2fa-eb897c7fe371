<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Button,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Select,
  SelectOption,
} from 'ant-design-vue';

const emit = defineEmits(['success']);

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const value = ref('由测试');
const radioOptions = [
  { label: '由测试', value: '由测试' },
  { label: '由方法', value: '由方法' },
];
const options = ref([
  { value: 'N/A', label: 'N/A' },
  { value: '动物实验组', label: '动物实验组' },
  { value: '含量测定', label: '含量测定' },
  { value: '微生物检验组', label: '微生物检验组' },
  { value: '性状', label: '性状' },
  { value: '检查', label: '检查' },
  { value: '理化检验组', label: '理化检验组' },
  { value: '生化检验组', label: '生化检验组' },
  { value: '系统管理组', label: '系统管理组' },
  { value: '细胞检定组', label: '细胞检定组' },
  { value: '综合管理组', label: '综合管理组' },
  { value: '质量监控组', label: '质量监控组' },
  { value: '鉴别', label: '鉴别' },
  { value: '系统管理组', label: '系统管理组' },
  { value: '细胞检定组', label: '细胞检定组' },
  { value: '综合管理组', label: '综合管理组' },
  { value: '质量监控组', label: '质量监控组' },
  { value: '鉴别', label: '鉴别' },
  { value: '系统管理组', label: '系统管理组' },
  { value: '细胞检定组', label: '细胞检定组' },
  { value: '综合管理组', label: '综合管理组' },
  { value: '质量监控组', label: '质量监控组' },
  { value: '鉴别', label: '鉴别' },
]);
const items = ref([
  { value: 'N/A', label: 'N/A' },
  { value: '动物实验组', label: '动物实验组' },
  { value: '含量测定', label: '含量测定' },
  { value: '微生物检验组', label: '微生物检验组' },
  { value: '性状', label: '性状' },
  { value: '检查', label: '检查' },
  { value: '理化检验组', label: '理化检验组' },
  { value: '生化检验组', label: '生化检验组' },
  { value: '系统管理组', label: '系统管理组' },
  { value: '细胞检定组', label: '细胞检定组' },
  { value: '综合管理组', label: '综合管理组' },
  { value: '质量监控组', label: '质量监控组' },
  { value: '鉴别', label: '鉴别' },
  { value: '系统管理组', label: '系统管理组' },
  { value: '细胞检定组', label: '细胞检定组' },
  { value: '综合管理组', label: '综合管理组' },
  { value: '质量监控组', label: '质量监控组' },
  { value: '鉴别', label: '鉴别' },
  { value: '系统管理组', label: '系统管理组' },
  { value: '细胞检定组', label: '细胞检定组' },
  { value: '综合管理组', label: '综合管理组' },
  { value: '质量监控组', label: '质量监控组' },
  { value: '鉴别', label: '鉴别' },
]);
const selectedItem = ref({ value: '', label: '' });
const clickItem = (item: { label: string; value: string }) => {
  selectedItem.value = item;
  console.warn(item);
};
const plainOptions = ref([
  { label: '方法1', value: '方法1' },
  { label: '方法2', value: '方法2' },
  { label: '方法3', value: '方法3' },
  { label: '方法4', value: '方法4' },
  { label: '方法5', value: '方法5' },
]);
const method = ref<string[]>([]);
const type = ref('');
</script>
<template>
  <Modal title="选择课程方法" class="h-3/6 w-1/3">
    <RadioGroup v-model:value="value">
      <Radio v-for="item of radioOptions" :value="item.value" :key="item.value">
        {{ item.label }}
      </Radio>
    </RadioGroup>
    <div
      v-if="value === '由测试'"
      class="flex h-[90%] flex-col border-2 border-solid border-gray-200 p-4"
    >
      <div class="py-2">
        请从左边的列表中选择一个分析，并在右边选择一个相关的方法。
      </div>
      <div>
        <span>测试类别：</span>
        <Select v-model:value="type" style="width: 150px">
          <SelectOption
            v-for="item in options"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>
      <div class="flex h-full flex-row">
        <div class="mr-4 w-1/2">
          <div class="pb-2">测试</div>

          <ul
            class="h-4/5 overflow-auto border-2 border-solid border-gray-200 p-2"
          >
            <li
              v-for="item in items"
              :key="item.value"
              class="list-item hover:bg-blue-200"
              :style="{
                backgroundColor:
                  selectedItem.value === item.value ? '#f0f0f0' : 'white',
              }"
              @click="clickItem(item)"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
        <div class="w-1/2 px-4">
          <div class="pb-2">方法</div>
          <div class="h-4/5 border-2 border-solid border-gray-200 p-2">
            <CheckboxGroup
              v-model="method"
              name="checkboxgroup"
              :options="plainOptions"
              class="grid"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="flex h-[90%] flex-col border-2 border-solid border-gray-200 p-4"
    >
      <div class="py-2">从列表中选择一个方法:</div>
      <div class="mr-4 h-full w-full">
        <div class="pb-2">测试</div>

        <ul
          class="h-5/6 overflow-auto border-2 border-solid border-gray-200 p-2"
        >
          <li
            v-for="item in items"
            :key="item.value"
            class="list-item hover:bg-blue-200"
            :style="{
              backgroundColor:
                selectedItem.value === item.value ? '#f0f0f0' : 'white',
            }"
            @click="clickItem(item)"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <Button type="primary" @click="modalApi.onConfirm">
        {{ $t('equipment.add') }}
      </Button>
      <Button type="primary" @click="modalApi.close">
        {{ $t('equipment.addAndClose') }}
      </Button>
      <Button @click="modalApi.close"> {{ $t('equipment.close') }}</Button>
    </template>
  </Modal>
</template>
