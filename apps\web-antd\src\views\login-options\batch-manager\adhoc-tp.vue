<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addAdHocSample,
  cbMfgEquip,
  getMatInfo,
  getMatInfoList,
  getRequestReason,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formRef = ref();
async function onAdHocSubmit(values: Record<string, any>) {
  try {
    console.log('开始提交常规样表单数据:', values);

    const data = values as BatcheManagerApi.Batches;
    data.SKIPSAMP = data.SKIPSAMP ? 'Y' : 'N';
    data.SKIPREC = data.SKIPREC ? 'Y' : 'N';

    await addAdHocSample(data);
    emit('success');

    console.log('常规样表单提交成功');
    return { success: true, data };
  } catch (error) {
    console.error('常规样表单提交失败:', error);
    throw error; // 重新抛出错误，让调用方处理
  }
}

const [AdHocForm, adHocFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onAdHocSubmit,
  layout: 'vertical',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'PLANT',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await cbMfgEquip('ADHOC');
          return res.items.map((item: { Text: string; Value: string }) => ({
            label: item.Text,
            value: item.Value,
          }));
        },
        immediate: true,
      },
      label: $t('login-options.batchManager.plant'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'PROCESS',
      label: $t('login-options.batchManager.process'),
    },
    {
      component: 'Input',
      fieldName: 'RETEST_BATCHID',
      label: $t('login-options.batchManager.retestBatchId'),
    },
    {
      component: 'Input',
      fieldName: 'RETESTORDNO',
      label: $t('login-options.batchManager.retestOrderNo'),
    },
    {
      component: 'ApiSelect',
      fieldName: 'REQUEST_REASON',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getRequestReason();
          return res.items.map((item: { TEXT: string; VALUE: string }) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        immediate: true,
      },

      label: $t('login-options.batchManager.requestReason'),
    },
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.batchManager.batchno'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'SAMPLEDESC',
      label: $t('login-options.batchManager.sampleDesc'),
    },
    {
      component: 'DatePicker',
      fieldName: 'ENDDATE',
      label: $t('login-options.batchManager.endDate'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'MATCODE',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getMatInfoList.bind(null, '', '', ''),
        labelField: 'MATCODE',
        valueField: 'MATCODE',
        onChange: async (value: string) => {
          const matinfo = await getMatInfo(value);
          if (matinfo.length > 0) {
            const matName = matinfo[0];
            adHocFormApi.setFieldValue('MATNAME', matName);
          }
        },
      },
      label: $t('login-options.batchManager.matcode'),
    },
    {
      component: 'Input',
      fieldName: 'MATNAME',
      label: $t('login-options.batchManager.matname'),
    },
    {
      component: 'DatePicker',
      fieldName: 'SAMPDATE',
      label: $t('login-options.batchManager.sampDate'),
    },
    {
      component: 'Input',
      fieldName: 'SAMPLEDBY',
      label: $t('login-options.batchManager.sampleBy'),
    },
    {
      component: 'Checkbox',
      fieldName: 'SKIPSAMP',
      label: $t('login-options.batchManager.skipSamp'),
    },
    {
      component: 'Checkbox',
      fieldName: 'SKIPREC',
      label: $t('login-options.batchManager.skipRec'),
    },
  ],
  // wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-8',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  onConfirm: async () => {
    await adHocFormApi.validateAndSubmitForm();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // await RawMaterialForm.resetFields();
    }
  },
});

// 暴露表单API供父组件调用
defineExpose({
  adHocFormApi,
  onAdHocSubmit,
});
</script>

<template>
  <Modal title="常规样登录" class="h-[1000px] w-[1200px]">
    <Page>
      <AdHocForm ref="formRef" />
    </Page>
  </Modal>
</template>
