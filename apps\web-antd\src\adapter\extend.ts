import type { VxeUIExport } from 'vxe-table';

import { $t } from '@vben/locales';

export function extendsDefaultFormatter(vxeUI: VxeUIExport) {
  vxeUI.formats.add('formatTranslate', {
    tableCellFormatMethod({ cellValue }) {
      // 存在翻译则使用翻译值，否则使用原值
      const traslateKey = `format-cell-traslate.${cellValue}`;
      const traslateVal = $t(traslateKey);
      if (traslateVal === traslateKey) {
        return cellValue;
      }
      return traslateVal;
    },
  });
}
