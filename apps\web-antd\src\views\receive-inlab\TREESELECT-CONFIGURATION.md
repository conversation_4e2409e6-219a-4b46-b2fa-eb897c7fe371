# TreeSelect 组件完整配置方案

## 📊 实现概述

在 `select-receive-date-location.vue` 文件中实现了完整的 TreeSelect 组件配置，支持：
- API数据加载和转换
- 自定义字段映射
- 动态子节点加载
- 搜索和过滤功能
- 完整的事件处理

## ✅ 核心功能实现

### 1. API数据加载和转换

#### 数据转换函数
```typescript
const transformToTreeData = (flatData: any[]) => {
  if (!flatData || flatData.length === 0) return [];
  
  const map = new Map();
  const roots: any[] = [];
  
  // 第一遍遍历：创建所有节点
  flatData.forEach(item => {
    const node = {
      label: item.TEXTMEMBER,
      value: item.VALUEMEMBER,
      key: item.VALUEMEMBER,
      parentId: item.PARENTMEMBER,
      children: [],
      ...item, // 保留原始数据
    };
    map.set(item.VALUEMEMBER, node);
  });
  
  // 第二遍遍历：建立父子关系
  flatData.forEach(item => {
    const node = map.get(item.VALUEMEMBER);
    if (item.PARENTMEMBER && map.has(item.PARENTMEMBER)) {
      const parent = map.get(item.PARENTMEMBER);
      parent.children.push(node);
    } else {
      roots.push(node);
    }
  });
  
  return roots;
};
```

#### 异步数据加载
```typescript
const loadLocationData = async (parentValue?: string) => {
  try {
    isLoading.value = true;
    const result = await getLocationList('SITE1');
    
    if (result && result.items) {
      if (parentValue) {
        // 动态加载子节点
        return transformToTreeData(
          result.items.filter(item => item.PARENTMEMBER === parentValue)
        );
      } else {
        // 初始加载所有数据
        const transformedData = transformToTreeData(result.items);
        treeData.value = transformedData;
        return transformedData;
      }
    }
    return [];
  } catch (error) {
    console.error('加载位置数据失败:', error);
    message.error('加载位置数据失败');
    return [];
  } finally {
    isLoading.value = false;
  }
};
```

### 2. 字段映射配置

#### 基本字段映射
```typescript
// 字段映射配置
labelField: 'TEXTMEMBER',     // API返回的label字段
valueField: 'VALUEMEMBER',    // API返回的value字段
childrenField: 'children',    // 子节点字段

// TreeSelect特有配置
fieldNames: {
  label: 'TEXTMEMBER',
  value: 'VALUEMEMBER', 
  children: 'children',
},
```

#### 数据结构映射
- **API返回字段** → **TreeSelect字段**
- `TEXTMEMBER` → `label` (显示文本)
- `VALUEMEMBER` → `value` (选中值)
- `PARENTMEMBER` → `parentId` (父节点ID)

### 3. 动态子节点加载

#### 懒加载配置
```typescript
loadData: async (treeNode: any) => {
  // 动态加载子节点
  if (treeNode.children && treeNode.children.length > 0) {
    return; // 已有子节点，不需要加载
  }
  
  try {
    const children = await loadLocationData(treeNode.value);
    treeNode.children = children;
  } catch (error) {
    console.error('动态加载子节点失败:', error);
  }
},
```

### 4. 搜索和过滤功能

#### 搜索配置
```typescript
// 搜索配置
treeNodeFilterProp: 'TEXTMEMBER',
filterTreeNode: (inputValue: string, treeNode: any) => {
  if (!inputValue) return true;
  const label = treeNode.TEXTMEMBER || treeNode.label || '';
  return label.toLowerCase().includes(inputValue.toLowerCase());
},
```

### 5. 完整的 Schema 配置

```typescript
{
  component: 'ApiTreeSelect',
  fieldName: 'LOCATIONCODE',
  label: $t('receive-inlab.locationcode'),
  rules: 'required',
  componentProps: {
    allowClear: true,
    placeholder: '请选择位置',
    showSearch: true,
    treeDefaultExpandAll: false,
    
    // API配置
    api: loadLocationData,
    immediate: true,
    
    // 字段映射配置
    labelField: 'TEXTMEMBER',
    valueField: 'VALUEMEMBER',
    childrenField: 'children',
    
    // TreeSelect特有配置
    fieldNames: {
      label: 'TEXTMEMBER',
      value: 'VALUEMEMBER',
      children: 'children',
    },
    
    // 搜索配置
    treeNodeFilterProp: 'TEXTMEMBER',
    filterTreeNode: (inputValue, treeNode) => {
      if (!inputValue) return true;
      const label = treeNode.TEXTMEMBER || treeNode.label || '';
      return label.toLowerCase().includes(inputValue.toLowerCase());
    },
    
    // 动态加载配置
    loadData: async (treeNode) => {
      if (treeNode.children && treeNode.children.length > 0) return;
      
      try {
        const children = await loadLocationData(treeNode.value);
        treeNode.children = children;
      } catch (error) {
        console.error('动态加载子节点失败:', error);
      }
    },
    
    // 事件处理
    onSelect: (value, node) => {
      console.log('选中节点:', { value, node });
    },
    
    onTreeExpand: (expandedKeys) => {
      console.log('展开的节点:', expandedKeys);
    },
    
    // 加载状态
    loading: isLoading,
  },
}
```

## 🔧 关键配置说明

### API数据加载
- **api**: 数据加载函数
- **immediate**: 是否立即加载数据
- **loading**: 加载状态控制

### 字段映射
- **labelField**: 指定显示文本的字段名
- **valueField**: 指定选中值的字段名
- **childrenField**: 指定子节点的字段名
- **fieldNames**: TreeSelect组件的字段名映射

### 动态加载
- **loadData**: 动态加载子节点的函数
- **treeDefaultExpandAll**: 是否默认展开所有节点

### 搜索功能
- **showSearch**: 启用搜索功能
- **treeNodeFilterProp**: 搜索时使用的字段
- **filterTreeNode**: 自定义搜索过滤函数

## 🎯 使用效果

- ✅ **API数据加载**: 自动调用API获取树形数据
- ✅ **字段映射**: 正确映射API字段到TreeSelect字段
- ✅ **动态加载**: 支持懒加载子节点
- ✅ **搜索过滤**: 支持按名称搜索节点
- ✅ **事件处理**: 完整的选择和展开事件处理
- ✅ **加载状态**: 显示数据加载状态

这个配置提供了完整的TreeSelect功能，支持复杂的树形数据结构和用户交互。
