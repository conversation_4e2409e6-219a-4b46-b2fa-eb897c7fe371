<script lang="ts" setup>
import type { ComponentPublicInstance } from 'vue';

import type { TestManagerApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message, Step, Steps } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  $addAnalyteApi,
  $addRoundingApi,
  $addTestApi,
  $getAnalyteApi,
  $getAnalyteTypeApi,
  $getCboEqTypeApi,
  $getCboMethodApi,
  $getCboServgrpApi,
  $getTestCatNoAllApi,
  $getUnitApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';

import PassibleResults from './passible-results.vue';
import PromptRounding from './prompt-rounding.vue';

const emit = defineEmits(['success']);
const currentTab = ref<number>(0);
const currentIndex = ref(0);
const testCode = ref(0);
const addAnalyte = ref<TestManagerApi.Analyte>();
const mode = ref(''); // 'analyteApp' | 'testApp'

async function onFirstSubmit(values: Record<string, any>) {
  const addTestCode = await $addTestApi(values);
  if (!addTestCode) {
    message.error($t('business-static-tables.testManager.addTestCodeFail'));
    return;
  }
  testCode.value = addTestCode as number;
  currentTab.value = 1;
  currentIndex.value = currentIndex.value + 1;
}

function onSecondReset() {
  currentTab.value = 0;
}

async function onSecondSubmit(values: Record<string, any>) {
  values.TESTCODE = testCode.value;
  const addRes = await $addAnalyteApi(values);
  if (addRes === false) {
    message.error($t('business-static-tables.testManager.addAnalyteFail'));
    return;
  }
  addAnalyte.value = values as TestManagerApi.Analyte;
  addAnalyte.value.ORIGREC = addRes;
  currentIndex.value =
    addAnalyte.value.ANALTYPE === 'N'
      ? currentIndex.value + 2
      : currentIndex.value + 1;
  currentTab.value = addAnalyte.value.ANALTYPE === 'N' ? 3 : 2;
  modalApi.setState({ confirmDisabled: false });
}

const [TestForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onFirstSubmit,
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getTestCatNoAllApi,
        labelField: 'TESTCATCODE',
        valueField: 'TESTCATCODE',
        class: 'w-full',
        showSearch: true,
      },
      fieldName: 'TESTCATCODE',
      label: $t('business-static-tables.testManager.testCatCode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'TESTNO',
      label: $t('business-static-tables.testManager.testNo'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'TESTNO_ENG',
      label: $t('business-static-tables.testManager.testNoEng'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getCboMethodApi,
        labelField: 'METHOD',
        valueField: 'METHOD',
        class: 'w-full',
        showSearch: true,
      },
      fieldName: 'METHOD',
      label: $t('business-static-tables.methodManager.method'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getCboServgrpApi,
        labelField: 'SERVGRP',
        valueField: 'SERVGRP',
        class: 'w-full',
        showSearch: true,
      },
      fieldName: 'SERVGRP',
      label: $t('commons.servGrp'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getCboEqTypeApi,
        labelField: 'EQTYPE',
        valueField: 'EQTYPE',
        class: 'w-full',
        showSearch: true,
      },
      fieldName: 'EQTYPE',
      label: $t('commons.eqType'),
    },
    {
      component: 'Checkbox',
      fieldName: 'ISALLEQ',
      label: $t('business-static-tables.testManager.isAllEq'),
      dependencies: {
        triggerFields: ['EQTYPE'],
        disabled(values) {
          return !values.EQTYPE || values.EQTYPE === 'N/A';
        },
      },
    },
    {
      component: 'Checkbox',
      fieldName: 'CHECKEQSTATUS',
      label: $t('business-static-tables.testManager.checkEqStatus'),
      dependencies: {
        triggerFields: ['EQTYPE'],
        disabled(values) {
          return !values.EQTYPE || values.EQTYPE === 'N/A';
        },
      },
    },
    {
      component: 'Checkbox',
      fieldName: 'NEEDCERT',
      label: $t('business-static-tables.testManager.needCert'),
    },
  ],
  submitButtonOptions: {
    content: '下一步',
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

const [AnalyteForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleReset: onSecondReset,
  handleSubmit: onSecondSubmit,
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
    // content: '上一步',
  },
  submitButtonOptions: {
    content: '下一步',
  },
  schema: [
    {
      component: 'AutoComplete',
      componentProps: {
        allowClear: true,
        class: 'w-full',
        filterOption(input: string, option: { value: string }) {
          return option.value.toLowerCase().includes(input.toLowerCase());
        },
        options: [],
        onSearch: async (value: string) => {
          if (!value) {
            return [];
          }
          const res = await $getAnalyteApi(-1);
          return res.map((item: TestManagerApi.Analyte) => ({
            label: item.ANALYTE,
            value: item.ANALYTE,
          }));
        },
      },
      fieldName: 'ANALYTE',
      label: $t('business-static-tables.testManager.analyte'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ANALYTE_ENG',
      label: $t('business-static-tables.testManager.analyteEng'),
    },
    {
      component: 'Input',
      fieldName: 'SINONYM',
      label: $t('business-static-tables.testManager.synonym'),
      rules: 'required',
      dependencies: {
        triggerFields: ['ANALYTE'],
        trigger(values, form) {
          form.setFieldValue('SINONYM', values.ANALYTE);
        },
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getAnalyteTypeApi,
        labelField: 'ANALYTE_TYPE_TEXT',
        valueField: 'ANALYTE_TYPE',
        class: 'w-full',
        showSearch: true,
      },
      fieldName: 'ANALTYPE',
      label: $t('business-static-tables.testManager.analType'),
      rules: 'required',
    },
    {
      fieldName: 'SCHEMANAME',
      component: 'Input',
      label: $t('business-static-tables.testManager.schemaName'),
      dependencies: {
        triggerFields: ['ANALTYPE'],
        trigger(values, form) {
          if (!values.ANALTYPE) {
            form.setFieldValue('SCHEMANAME', '');
            return;
          }
          // 等于N是需要单位
          if (values.ANALTYPE === 'N') {
            form.setFieldValue('SCHEMANAME', 'NUMERIC_NA');
          } else {
            form.setFieldValue('SCHEMANAME', 'CHAR_NA');
          }
        },
      },
    },
    {
      fieldName: 'UNITS',
      label: $t('business-static-tables.testManager.units'),
      component: 'ApiSelect',
      componentProps: {
        api: $getUnitApi,
        labelField: 'UNIT_CODE',
        valueField: 'UNIT_CODE',
        class: 'w-full',
        showSearch: true,
      },
      dependencies: {
        triggerFields: ['ANALTYPE'],
        show(values) {
          // 等于N是需要单位
          return values.ANALTYPE === 'N';
        },
      },
    },
    {
      fieldName: 'NOREP',
      label: $t('business-static-tables.testManager.noRep'),
      component: 'InputNumber',
      componentProps: {
        min: 1,
        defaultValue: 1,
      },
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [tabTitleMap.value[currentTab.value]]);
});

const tabTitleMap = ref<string[]>([
  $t('business-static-tables.testManager.test'),
  $t('business-static-tables.testManager.analyte'),
  $t('business-static-tables.testManager.possibleResult'),
  $t('business-static-tables.testManager.picture'),
]);

const passibleRef = ref<ComponentPublicInstance<{
  HasPossibleResults: () => Promise<boolean>;
}> | null>(null);

const pictureRef = ref<ComponentPublicInstance<{
  getResultFormat: () => Promise<string>;
}> | null>(null);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: true,
  onConfirm: async () => {
    if (currentTab.value === 2) {
      let hasValue = false;
      if (passibleRef.value) {
        hasValue = await passibleRef.value.HasPossibleResults();
      }
      if (!hasValue) {
        message.error('请至少添加一条可能的结果');
        return;
      }
      emit('success');
      modalApi.close();
    } else {
      let format = '';
      if (pictureRef.value) {
        format = await pictureRef.value.getResultFormat();
      }
      if (!format) {
        return;
      }
      await $addRoundingApi({
        origrec: addAnalyte.value?.ORIGREC ?? 0,
        rounding: format,
      });
      emit('success');
      modalApi.close();
    }
  },

  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData();
      if (data && data.currTestCode && data.mode) {
        testCode.value = data.currTestCode;
        mode.value = data.mode;
        tabTitleMap.value = [
          $t('business-static-tables.testManager.analyte'),
          $t('business-static-tables.testManager.possibleResult'),
          $t('business-static-tables.testManager.picture'),
        ];
        currentTab.value = 1;
      }
    }
  },

  onCancel: () => {
    modalApi.close();
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Page>
      <div class="mx-auto">
        <Steps :current="currentIndex" class="steps">
          <Steps :current="currentIndex" class="steps">
            <template v-for="(title, index) in tabTitleMap" :key="index">
              <Step :title="title" />
            </template>
          </Steps>
        </Steps>
        <div class="p-0">
          <TestForm v-show="currentTab === 0" class="p-10" />
          <AnalyteForm v-show="currentTab === 1" class="p-10" />
          <PassibleResults
            v-if="currentTab === 2"
            :analyte="addAnalyte!"
            mode="Wizard"
            ref="passibleRef"
          />
          <PromptRounding
            v-if="currentTab === 3"
            class="p-10"
            ref="pictureRef"
          />
        </div>
      </div>
    </Page>
  </Modal>
</template>
