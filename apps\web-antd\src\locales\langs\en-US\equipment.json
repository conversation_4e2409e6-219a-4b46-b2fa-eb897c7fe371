{"title": "Equipment", "save": "Save", "edit": "Edit", "cancel": "Cancel", "coloseEdit": "ColoseEdit", "add": "Add", "delete": "Delete", "clear": "Clear", "close": "Close", "addAndClose": "Add & Close", "editCalendarEvent": "Edit Calendar Event", "equipment-mg": {"title": "Equipment manager", "editAnalysisItemList": "EditAnalysisItemList", "equipmentId": "EquipmentId", "equipmentType": "EquipmentType", "equipmentName": "EquipmentName", "status": "Status", "selectReport": "SelectReport", "manufacturer": "Manufacturer", "model": "Model", "modelNo": "ModelNo", "primaryResponsibleParty": "PrimaryResponsibleParty", "storageLocation": "StorageLocation", "secondaryResponsibleParty": "SecondaryResponsibleParty", "equipmentStatus": "EquipmentStatus", "installationDate": "InstallationDate", "activationDate": "ActivationDate", "offlineStatus": "OfflineStatus", "associatedEquipment": "AssociatedEquipment", "calibrationInstitution": "CalibrationInstitution", "calibrationParameters": "CalibrationParameters", "softwareName": "SoftwareName", "softwareVersion": "SoftwareVersion", "scaleCapacity": "ScaleCapacity", "scalePrecision": "ScalePrecision", "equipmentCode": "EquipmentCode", "verificationSeparation": "VerificationSeparation", "usageRecordELN": "UsageRecordELN", "serialNumber": "SerialNumber", "editCalendarEvent": "EditCalendarEvent", "deviceEvent": "DeviceEvent", "description": "Description", "lastMaintenanceTime": "LastMaintenanceTime", "maintenanceFrequency": "MaintenanceFrequency", "reminderwindow": "Reminderwindow", "affectinstrumentstatus": "Affectinstruments<PERSON><PERSON>", "mainStatus": "MainStatus", "mainexpdate": "Mainexpdate", "position": "Position", "isOwner": "<PERSON><PERSON><PERSON><PERSON>", "openMainEvent": "OpenMainEvent", "finished": "Finished", "disable": "Disable", "runELN": "RUN ELN", "initELN": "INIT ELN", "viewELN": "VIEW ELN", "addCertificate": "AddCertificate", "checkCertificate": "CheckCertificate", "search": "Search", "launchELN": "LaunchELN", "inspectELN": "InspectELN", "maintenancetype": "Maintenancetype", "maintenanceEvent": "MaintenanceEvent", "opendate": "Opendate", "openReason": "OpenReason", "maintDate": "MaintDate ", "remarks": "Remarks", "actionTaken": "ActionTaken", "ElNID": "ElNID", "ELN_ORIGREC": "ELN_ORIGREC", "certificateName": "CertificateName", "certificateDescription": "CertificateDescription", "controlName": "ControlName", "analysisItem": "AnalysisItem", "inspectionItemCode": "InspectionItemCode", "inspectionItemName": "InspectionItemName", "analysisItemSynonym": "AnalysisItemSynonym", "collectionSampleNo": "CollectionSampleNo", "documentID": "DocumentID", "addTime": "AddTime", "viewSpectrum": "ViewSpectrum", "openBy": "OpenBy", "maintBy": "MaintBy", "viewDocument": "ViewDocument", "documentName": "DocumentName", "attachmentID": "AttachmentID", "add": "Add", "usedDate": "UsedDate", "deviceStartTime": "DeviceStartTime", "deviceStopTime": "DeviceStopTime", "usedSample": "UsedSample", "checkID": "CheckID", "checkSpec": "CheckSpec", "usedProject": "UsedProject", "checkProject": "CheckProject", "usedPerson": "<PERSON><PERSON><PERSON>", "ELNModelID": "ELNModelID", "ELNRunID": "ELNRunID", "DCUMethod": "DCUMethod", "fileDirectory": "FileDirectory", "sampleFile": "SampleFile", "baudRate": "BaudRate", "stopBit": "StopBit", "RTSStopSend": "RTS(stopSend)", "databits": "Databits", "stop": "Stop", "DTRDataTerminalRead": "DTR(dataTerminalRead)", "parityCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "COMPort": "COMPort", "parsingMethod": "ParsingMethod", "IPAdress": "IPAdress", "portNumber": "PortNumber", "terminator": "Terminator", "scriptTriggerCharacter": "ScriptTriggerCharacter", "analysisScript": "AnalysisScript", "commandName": "CommandName", "command": "Command", "commandHexView": "CommandHexView", "deviceList": "DeviceList", "maintainType": "MaintainType:", "event": "Event", "reason": "Reason", "activationDevice": "ActivationDevice", "not": "Not", "operateSymbol": "OperateSymbol", "value": "Value", "logic": "Logic", "selectCOMPort": "SelectCOMPort", "ipAddress": "ip<PERSON><PERSON><PERSON>", "port": "Port", "parseMethod": "Parse<PERSON><PERSON><PERSON>", "inspectionCode": "Inspection Code", "inspectionName": "Inspection Name", "analysisItemSynonyms": "Analysis Item Synonyms", "sampleCollectionNumber": "Sample Collection Number", "documentId": "Document ID", "creationTime": "Creation Time", "creator": "Creator", "sampleNumber": "Sample Number", "absorbance": "Absorbance", "blank": "Blank", "dwellTime": "Dwell Time", "correctedConcentration": "Corrected Concentration", "peakArea": "Peak Area", "peakAreaPercentage": "Peak Area Percentage", "symmetryFactor": "Symmetry Factor", "plateCount": "Plate Count", "resolution": "Resolution", "signalNoise": "Signal Noise", "signalToNoiseRatio": "Signal To Noise Ratio", "retentionTimeRSD": "Retention Time RSD", "areaRSD": "Area RSD", "USPTailing": "USP Tailing", "calculatedIonRatio": "Calculated Ion Ratio", "wavelength": "Wavelength", "peakHeight": "Peak Height", "intercept": "Intercept", "slope": "Slope", "correlationCoefficient": "Correlation Coefficient", "scanCount": "<PERSON><PERSON>", "resolutionPower": "Resolution Power", "absorbance2": "Absorbance 2", "absorbance3": "Absorbance 3", "diameter1": "Diameter 1", "diameter2": "Diameter 2", "sampleDetectionValue": "Sample Detection Value", "SDValue": "SD Value", "meanValue": "Mean Value", "crackLength2": "Crack Length 2", "crackLength3": "Crack Length 3", "negativeControl": "Negative Control", "positiveControl": "Positive Control", "linearEquation": "Linear Equation", "standard1Concentration": "Standard 1 Concentration", "standard2Concentration": "Standard 2 Concentration", "standard3Concentration": "Standard 3 Concentration", "standard4Concentration": "Standard 4 Concentration", "standard5Concentration": "Standard 5 Concentration", "standard6Concentration": "Standard 6 Concentration", "standard1Absorbance": "Standard 1 Absorbance", "standard2Absorbance": "Standard 2 Absorbance", "standard3Absorbance": "Standard 3 Absorbance", "standard4Absorbance": "Standard 4 Absorbance", "standard5Absorbance": "Standard 5 Absorbance", "standard6Absorbance": "Standard 6 Absorbance", "standard1CycleNumber": "Standard 1 Cycle Number", "standard2CycleNumber": "Standard 2 Cycle Number", "standard3CycleNumber": "Standard 3 Cycle Number", "processNegativeCycle1": "Process Negative Cycle 1", "processNegativeCycle2": "Process Negative Cycle 2", "processNegativeCycle3": "Process Negative Cycle 3", "sampleNegativeCycle1": "Sample Negative Cycle 1", "sampleNegativeCycle2": "Sample Negative Cycle 2", "sampleNegativeCycle3": "Sample Negative Cycle 3", "standard1Diameter": "Standard 1 Diameter", "standard2Diameter": "Standard 2 Diameter", "standard3Diameter": "Standard 3 Diameter", "standard4Diameter": "Standard 4 Diameter", "standard5Diameter": "Standard 5 Diameter", "standard6Diameter": "Standard 6 Diameter", "standard7Diameter": "Standard 7 Diameter", "standard8Diameter": "Standard 8 Diameter", "standard9Diameter": "Standard 9 Diameter", "standard10Diameter": "Standard 10 Diameter", "standard11Diameter": "Standard 11 Diameter", "standard12Diameter": "Standard 12 Diameter", "standard13Diameter": "Standard 13 Diameter", "standard14Diameter": "Standard 14 Diameter", "standard15Diameter": "Standard 15 Diameter", "standard16Diameter": "Standard 16 Diameter", "standard17Diameter": "Standard 17 Diameter", "standard18Diameter": "Standard 18 Diameter", "standard19Diameter": "Standard 19 Diameter", "standard20Diameter": "Standard 20 Diameter", "TOCResult": "TOC Result", "TOCAverage": "TOC Average", "name": "Name", "freezingPointName": "Freezing Point Name", "number": "Number", "ICAverage": "IC Average", "TCAverage": "TC Average", "CPMValue": "CPM Value", "CPMAverage": "CPM Average", "tailingFactor": "Tailing Factor", "temperatureRise": "Temperature Rise"}, "main-form": {"type": "Type", "title": "BalanceDayCalibration", "model": "Model", "manufacturer": "Manufacturer", "balanceRange": "BalanceRange", "balancePrecision": "BalancePrecision", "verificationScale": "VerificationScale", "initiateCalibration": "InitiateCalibration", "completeCalibration": "CompleteCalibration", "query": "Query", "addAttachment": "AddAttachment", "viewAttachment": "ViewAttachment", "deleteAttachment": "DeleteAttachment", "recordId": "RecordID", "eventType": "EventType", "event": "Event", "status": "Status", "weightId": "WeightID", "initiator": "Initiator", "initiationTime": "InitiationTime", "maintainer": "Maintainer", "maintenanceTime": "MaintenanceTime", "initiationReason": "InitiationReason", "remark": "Remark", "addTargetPoint": "AddTargetPoint", "addCheckPoint": "AddCheckPoint", "delete": "Delete", "connectBalance": "ConnectBalance", "weight": "weight", "reason": "reason"}, "course-form": {"title": "CourseManage", "printReport": "PrintReport", "courseCode": "CourseCode", "courseName": "CourseName", "provider": "Provider", "fee": "Fee", "additionalDocuments": "AdditionalDocuments", "viewDocuments": "ViewDocuments", "deleteDocuments": "DeleteDocuments", "editList": "EditList", "removeMethod": "RemoveMethod", "summary": "Summary", "method": "Method", "testName": "TestName", "position": "Position"}, "course-schedules": {"title": "TrainingPlan"}}