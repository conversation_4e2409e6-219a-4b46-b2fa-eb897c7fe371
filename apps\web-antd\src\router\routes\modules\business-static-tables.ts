import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: $t('business-static-tables.title'),
    },
    name: 'business-static-tables',
    path: '/business-static-tables',
    children: [
      {
        meta: {
          title: $t('business-static-tables.process-specifications.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'process-specifications',
        path: '/business-static-tables/process-specifications',
        component: () =>
          import(
            '#/views/business-static-tables/process-specifications/spec-manager.vue'
          ),
      },
      {
        meta: {
          title: $t('business-static-tables.sampleGroups.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'sampleGroups',
        path: '/business-static-tables/sample-groups',
        component: () =>
          import(
            '#/views/business-static-tables/sample-groups/sample-groups.vue'
          ),
      },
      {
        meta: {
          title: $t('business-static-tables.word-static-fields.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'word-static-fields',
        path: '/business-static-tables/word-static-fields',
        component: () =>
          import(
            '#/views/business-static-tables/word-static-fields/static-fields.vue'
          ),
      },
      {
        meta: {
          title: $t('business-static-tables.word-template.title'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'word-template',
        path: '/business-static-tables/word-template',
        component: () =>
          import(
            '#/views/business-static-tables/word-template/word-template.vue'
          ),
      },
      {
        meta: {
          title: $t('business-static-tables.methodManager.menu'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'methodManager',
        path: '/businessstatictable/methodManager',
        component: () =>
          import(
            '#/views/business-static-tables/method-manager/method-manager.vue'
          ),
      },
      {
        meta: {
          title: $t('business-static-tables.specSchema.menu'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'specSchema',
        path: '/businessstatictable/specSchema',
        component: () =>
          import(
            '#/views/business-static-tables/spec-schema/spec-schema-main.vue'
          ),
      },
      {
        meta: {
          title: $t('business-static-tables.testManager.menu'),
          icon: 'ic:baseline-view-in-ar',
        },
        name: 'testManager',
        path: '/businessstatictable/testManager',
        component: () =>
          import(
            '#/views/business-static-tables/test-manager/test-manager.vue'
          ),
      },
    ],
  },
];
export default routes;
