<script lang="ts" setup>
import type { ReceiveInLabApi } from '#/api/receive-inlab/receive-inlab'; // 导入ReceiveInLabApi接口

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Col, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getPromptForSample,
  receiveInLabMulti,
  updReceiveAll,
} from '#/api/receive-inlab/receive-inlab';
import { $t } from '#/locales';
import { createGridOptions } from '#/utils/grid-option';

import {
  usePendingItemsColumns,
  useSelectedItemsColumns,
} from './receive-inlab-data';

// 定义组件事件
const emit = defineEmits(['success']);

const formData = ref();

// 当前 RecipeMaterialGrid 数据源
const recipeMaterialData = ref<any[]>([]);

const [Form] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  layout: 'vertical',
  schema: [
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '按样品',
            value: '1',
          },
          {
            label: '按库存',
            value: '2',
          },
          {
            label: '稳定性',
            value: '3',
          },
        ],
      },
      fieldName: 'radioGroup',
      label: '请选择接收方式',
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-100',
  showDefaultActions: false,
});

async function onSubmit() {}

const pendingGridOptions = {
  ...createGridOptions<ReceiveInLabApi.ReceiveOrders>(),
  columns: usePendingItemsColumns(),
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getPromptForSample('RECEIVEINCR', '');
      },
    },
  },
};

// 第一个表格配置
const [PendingGrid, pendingGridApi] = useVbenVxeGrid({
  gridOptions: pendingGridOptions,
});

const selectedGridOptions = {
  ...createGridOptions<ReceiveInLabApi.ReceiveOrders>(),
  columns: useSelectedItemsColumns(),
  proxyConfig: {
    ajax: {
      query: async () => {
        const recipeCode = formData.value?.RECIPECODE;
        if (typeof recipeCode !== 'number') {
          // message.warning('配方编码无效');
          return [];
        }

        const newRecipeData = await getPromptForSample('', '');

        // 先清空，再把接口的数据填充，避免添加的时候被覆盖
        recipeMaterialData.value = [];
        recipeMaterialData.value.push(...newRecipeData.items);

        return newRecipeData;
      },
    },
  },
};

// 第二个表格配置
const [SelectedGrid, selectedGridApi] = useVbenVxeGrid({
  gridOptions: selectedGridOptions,
});

// 新增
async function onCreate() {
  const selectedRows =
    pendingGridApi.grid?.getCheckboxRecords() as ReceiveInLabApi.ReceiveOrders[];

  if (selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 过滤掉已存在于 recipeMaterialData 的 MATCODE
  const existingMatCodes = new Set(
    recipeMaterialData.value.map((item) => item.Value),
  );

  const newRows = selectedRows.filter(
    (row) => !existingMatCodes.has(row.Value),
  );

  if (newRows.length === 0) {
    message.info('所选数据已添加过,请选择不同的数据！');
    return;
  }

  // 添加新数据
  recipeMaterialData.value = [...recipeMaterialData.value, ...newRows];

  // 刷新表格
  selectedGridApi.setGridOptions({
    data: recipeMaterialData.value,
  });
}

// 删除
async function onDelete() {
  const selectedRows = selectedGridApi.grid?.getCheckboxRecords();

  if (!selectedRows || selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 过滤掉被选中的行
  recipeMaterialData.value = recipeMaterialData.value.filter(
    (item) => !selectedRows.some((row) => row.Value === item.Value),
  );

  // 刷新表格
  selectedGridApi.setGridOptions({
    data: recipeMaterialData.value,
  });
}

const aSampleData = ref<string[][]>([]);

// 保存修改
async function saveChanges() {
  try {
    // 1. 获取表格中所有行数据
    const allData = selectedGridApi.grid?.getData();

    if (!allData || allData.length === 0) {
      message.warning('没有可接收的数据');
      return;
    }

    aSampleData.value = allData.map((item) => [
      item.ORDNO,
      item.Value,
      item.SAMPLEDBY,
      item.SAMPDATE,
      item.LOCATIONCODE,
      item.FOLDERNO,
    ]);
    console.log(aSampleData);
    const aORDNOs = await receiveInLabMulti(
      'SAMPLE',
      aSampleData.value,
      new Date(),
      'RECEIVEINCR',
    );
    if (aORDNOs) {
      await updReceiveAll(aORDNOs);
    }
    // 实现保存逻辑
    message.success('接收成功');
    emit('success');
    // 关闭Modal
    modalApi.close();
  } catch (error) {
    console.warn('接收失败:', error);
    message.error('接收失败');
  }
}

// 获取Modal实例
const [Modal, modalApi] = useVbenModal({
  title: '扫码接收',
  onCancel() {
    modalApi.close();
  },
  onConfirm: saveChanges,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal class="h-[800px] w-[1350px]">
    <Form />
    <Row :gutter="16">
      <Col :span="11.5" class="h-[600px] overflow-hidden">
        <!-- 左侧 Grid -->
        <PendingGrid />
      </Col>
      <!-- 按钮区域 -->
      <Col :span="1" style="text-align: center">
        <div class="button-container">
          <Button type="primary" @click="onCreate">
            {{ $t('receive-inlab.right') }}
          </Button>
          <Button
            type="primary"
            danger
            :style="{ marginTop: '20px' }"
            @click="onDelete"
          >
            {{ $t('receive-inlab.left') }}
          </Button>
        </div>
      </Col>
      <Col :span="11.5" class="h-[600px] overflow-hidden">
        <!-- 右侧Grid -->
        <SelectedGrid />
      </Col>
    </Row>
  </Modal>
</template>

<style scoped>
.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
