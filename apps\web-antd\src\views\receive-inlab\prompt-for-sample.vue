<script lang="ts" setup>
import type { ReceiveInLabApi } from '#/api/receive-inlab/receive-inlab'; // 导入ReceiveInLabApi接口

import { computed, nextTick, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Col, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getPromptForSample,
  receiveInLabMulti,
  updReceiveAll,
} from '#/api/receive-inlab/receive-inlab';
import { $t } from '#/locales';
import { createGridOptions } from '#/utils/grid-option';

import {
  usePendingItemsColumns,
  useSelectedItemsColumns,
} from './receive-inlab-data';

// 定义组件事件
const emit = defineEmits(['success']);

const formData = ref();
const ReceiveMethod = ref<string>('SAMPLE');

// 数据状态管理
const originalPendingData = ref<ReceiveInLabApi.ReceiveOrders[]>([]); // 原始左侧数据
const selectedItemData = ref<ReceiveInLabApi.ReceiveOrders[]>([]); // 右侧已选择数据

// 计算过滤后的左侧数据（排除已选择的数据）
const filteredPendingData = computed(() => {
  const selectedValues = new Set(
    selectedItemData.value.map((item) => item.Value),
  );
  return originalPendingData.value.filter(
    (item) => !selectedValues.has(item.Value),
  );
});

// 监听右侧数据变化，自动刷新左侧表格
watch(
  selectedItemData,
  () => {
    refreshPendingGrid();
  },
  { deep: true },
);

const [Form] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  handleValuesChange: async (changedValues: any) => {
    // console.log(changedValues);
    // console.log(fieldsChanged);
    // 获取变化的字段名
    const changedField = Object.keys(changedValues)[0];
    // 判断是否是 radioGroupValue 字段发生了变化
    if (changedField === 'ReceiveMethod') {
      const newValue = changedValues[changedField];
      ReceiveMethod.value = newValue;
      const data = await getPromptForSample('RECEIVEINCR', newValue);
      pendingGridApi.setGridOptions({
        data: data.items,
      });
    }
  },
  layout: 'vertical',
  schema: [
    {
      component: 'RadioGroup',
      rules: 'required',
      componentProps: {
        options: [
          {
            label: '按样品',
            value: 'SAMPLE',
          },
          {
            label: '按库存',
            value: 'INVENTORY',
          },
          {
            label: '稳定性',
            value: 'Stability',
          },
        ],
      },
      fieldName: 'ReceiveMethod',
      label: '请选择接收方式',
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-100',
  showDefaultActions: false,
});

async function onSubmit() {}

// 刷新左侧表格的方法
const refreshPendingGrid = () => {
  if (pendingGridApi.grid) {
    pendingGridApi.setGridOptions({
      data: filteredPendingData.value,
    });
  }
};

const pendingGridOptions = {
  ...createGridOptions<ReceiveInLabApi.ReceiveOrders>(),
  columns: usePendingItemsColumns(),
  proxyConfig: {
    ajax: {
      query: async () => {
        const result = await getPromptForSample(
          'RECEIVEINCR',
          ReceiveMethod.value,
        );
        // 保存原始数据
        originalPendingData.value = result.items || [];
        // 返回过滤后的数据
        return {
          items: filteredPendingData.value,
          total: filteredPendingData.value.length,
        };
      },
    },
  },
};

// 第一个表格配置
const [PendingGrid, pendingGridApi] = useVbenVxeGrid({
  gridOptions: pendingGridOptions,
});

const selectedGridOptions = {
  ...createGridOptions<ReceiveInLabApi.ReceiveOrders>(),
  columns: useSelectedItemsColumns(),
  data: [],
};

// 第二个表格配置
const [SelectedGrid, selectedGridApi] = useVbenVxeGrid({
  gridOptions: selectedGridOptions,
});

// 新增数据到右侧
async function onCreate() {
  const selectedRows =
    pendingGridApi.grid?.getCheckboxRecords() as ReceiveInLabApi.ReceiveOrders[];

  if (selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 由于左侧已经过滤了重复数据，这里直接添加即可
  const newRows = selectedRows.filter((row) => {
    // 双重检查，确保不会添加重复数据
    return !selectedItemData.value.some((item) => item.Value === row.Value);
  });

  if (newRows.length === 0) {
    message.info('所选数据已添加过,请选择不同的数据！');
    return;
  }

  // 添加新数据到右侧
  selectedItemData.value = [...selectedItemData.value, ...newRows];

  // 刷新右侧表格
  selectedGridApi.setGridOptions({
    data: selectedItemData.value,
  });

  // 清除左侧表格的选择状态
  pendingGridApi.grid?.clearCheckboxRow();

  // message.success(`成功添加 ${newRows.length} 条数据`);
}

// 从右侧删除数据
async function onDelete() {
  const selectedRows =
    selectedGridApi.grid?.getCheckboxRecords() as ReceiveInLabApi.ReceiveOrders[];

  if (!selectedRows || selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 记录删除的数据数量
  // const deleteCount = selectedRows.length;

  // 过滤掉被选中的行
  selectedItemData.value = selectedItemData.value.filter(
    (item) => !selectedRows.some((row) => row.Value === item.Value),
  );

  // 刷新右侧表格
  selectedGridApi.setGridOptions({
    data: selectedItemData.value,
  });

  // 清除右侧表格的选择状态
  selectedGridApi.grid?.clearCheckboxRow();

  // message.success(`成功移除 ${deleteCount} 条数据`);
}

const aSampleData = ref<string[][]>([]);

// 辅助方法：重置所有数据
const resetAllData = () => {
  originalPendingData.value = [];
  selectedItemData.value = [];
  pendingGridApi.grid?.clearCheckboxRow();
  selectedGridApi.grid?.clearCheckboxRow();
};

// 辅助方法：刷新所有表格
// const refreshAllGrids = () => {
//   // 刷新左侧表格
//   refreshPendingGrid();
//   // 刷新右侧表格
//   selectedGridApi.setGridOptions({
//     data: recipeMaterialData.value,
//   });
// };

// 保存修改
async function saveChanges() {
  try {
    // 1. 获取表格中所有行数据
    const allData = selectedGridApi.grid?.getData();

    if (!allData || allData.length === 0) {
      message.warning('没有可接收的数据');
      return;
    }

    aSampleData.value = allData.map((item) => [
      item.ORDNO,
      item.Value,
      item.SAMPLEDBY,
      item.SAMPDATE,
      item.LOCATIONCODE,
      item.FOLDERNO,
    ]);
    const sReceiveMode = ref();
    sReceiveMode.value =
      ReceiveMethod.value === 'SAMPLE' || ReceiveMethod.value === 'Stability'
        ? 'SAMPLE'
        : ReceiveMethod.value;

    const aORDNOs = await receiveInLabMulti(
      sReceiveMode.value,
      aSampleData.value,
      new Date(),
      'RECEIVEINCR',
    );
    if (aORDNOs) {
      await updReceiveAll(aORDNOs);
    }
    // 实现保存逻辑
    message.success('接收成功');
    emit('success');
    // 关闭Modal
    modalApi.close();
  } catch (error) {
    console.warn('接收失败:', error);
    message.error('接收失败');
  }
}

// 获取Modal实例
const [Modal, modalApi] = useVbenModal({
  title: '扫码接收',
  onCancel() {
    modalApi.close();
  },
  onConfirm: saveChanges,
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formData.value = modalApi.getData<Record<string, any>>();
      await nextTick();
      // 重置数据状态
      resetAllData();
      // 重新加载左侧数据
      pendingGridApi.query();
    }
  },
});
</script>

<template>
  <Modal class="h-[800px] w-[1350px]">
    <Form />
    <Row :gutter="16">
      <Col :span="11.5" class="h-[570px] overflow-hidden">
        <!-- 左侧 Grid -->
        <PendingGrid />
      </Col>
      <!-- 按钮区域 -->
      <Col :span="1" style="text-align: center">
        <div class="button-container">
          <Button type="primary" @click="onCreate">
            {{ $t('receive-inlab.right') }}
          </Button>
          <Button
            type="primary"
            danger
            :style="{ marginTop: '20px' }"
            @click="onDelete"
          >
            {{ $t('receive-inlab.left') }}
          </Button>
        </div>
      </Col>
      <Col :span="11.5" class="h-[570px] overflow-hidden">
        <!-- 右侧Grid -->
        <SelectedGrid />
      </Col>
    </Row>
  </Modal>
</template>

<style scoped>
.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
