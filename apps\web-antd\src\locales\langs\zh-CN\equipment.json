{"title": "组织-资源管理", "save": "保存", "edit": "编辑", "cancel": "取消", "coloseEdit": "关闭编辑", "add": "添加", "delete": "删除", "clear": "清除", "close": "关闭", "addAndClose": "添加&关闭", "editCalendarEvent": "编辑日程事件", "equipment-mg": {"title": "设备管理", "editAnalysisItemList": "编辑分析项列表", "equipmentType": "设备类型", "equipmentName": "设备名称", "status": "状态", "selectReport": "选择报表", "manufacturer": "生产厂家", "model": "规格", "modelNo": "型号", "primaryResponsibleParty": "第一责任人", "storageLocation": "存放位置", "secondaryResponsibleParty": "第二责任人", "equipmentStatus": "设备状态", "installationDate": "安装日期", "activationDate": "启用日期", "offlineStatus": "线下状态", "associatedEquipment": "关联器具", "calibrationInstitution": "校准机构", "calibrationParameters": "校准参数", "softwareName": "软件名称", "softwareVersion": "软件版本", "scaleCapacity": "天平量程", "scalePrecision": "天平精度", "equipmentCode": "设备编码", "verificationSeparation": "检定分离度", "usageRecordELN": "使用记录ELN", "serialNumber": "序列号", "editCalendarEvent": "编辑日程事件", "deviceEvent": "设备事件", "description": "描述", "lastMaintenanceTime": "上次维护时间", "maintenanceFrequency": "维护频率", "reminderwindow": "几天内提醒", "affectinstrumentstatus": "是否影响仪器状态", "mainStatus": "事件状态", "mainexpdate": "维护到期日", "position": "岗位", "isOwner": "是否管理岗位", "openMainEvent": "打开维护事件", "finished": "完成", "disable": "停用", "runELN": "RUN ELN", "initELN": "INIT ELN", "viewELN": "VIEW ELN", "addCertificate": "追加证书", "checkCertificate": "证书查看", "search": "查询", "launchELN": "运行ELN", "inspectELN": "查看ELN", "recordID": "记录ID", "maintenancetype": "事件类型", "maintenanceEvent": "事件", "opendate": "启用时间", "openReason": "启用原因", "openBy": "启用人", "maintDate": "维护时间 ", "maintBy": "维护人", "actionTaken": "采取行动", "ElNID": "ELN模板ID", "ELN_ORIGREC": "ELN运行ID", "certificateName": "证书名称", "certificateDescription": "证书描述", "controlName": "对照名", "inspectionItemCode": "检项代码", "inspectionItemName": "检项名称", "analysisItemSynonym": "分析项同义词", "collectionSampleNo": "采集样品号", "documentID": "文档ID", "addTime": "添加时间", "viewSpectrum": "查看图谱", "viewDocument": "查看文档", "documentName": "文件名称", "attachmentID": "附件ID", "add": "添加", "usedDate": "使用日期", "deviceStartTime": "设备起始时间", "deviceStopTime": "设备停止时间", "usedSample": "使用样品", "checkID": "检品批号", "checkSpec": "检品规格", "usedProject": "使用项目", "checkProject": "检品项目", "usedPerson": "使用人", "ELNModelID": "ELN模板ID", "ELNRunID": "ELN运行ID", "DCUMethod": "DCU方法", "fileDirectory": "文件目录", "sampleFile": "样品文件", "baudRate": "波特率", "stopBit": "停止位", "RTSStopSend": "RTS(停止发送)", "databits": "数据位", "stop": "终止", "DTRDataTerminalRead": "DTR (数据终端就绪)", "parityCheck": "奇偶校验", "COMPort": "COM端口", "parsingMethod": "解析方法", "IPAdress": "IP地址", "portNumber": "端口号", "terminator": "终止符", "scriptTriggerCharacter": "脚本触发器字符", "analysisScript": "分析脚本", "commandName": "命令名", "command": "命令", "commandHexView": "命令十六进制视图", "deviceList": "设备列表", "maintainType": "维护类型:", "event": "事件", "reason": "原因", "activationDevice": "启用设备", "not": "非", "operateSymbol": "操作符", "value": "值", "logic": "逻辑", "selectCOMPort": "选择COM端口", "ipAddress": "IP地址", "port": "端口号", "parseMethod": "解析方法", "baseUrl": "基础URL", "resultRequestScript": "基于运行的结果请求服务器脚本", "calibrationRequestScript": "校准请求服务器脚本", "measurementTypeFilter": "测量类型过滤器", "unitFilter": "单位过滤器", "inspectionCode": "检项代码", "inspectionName": "检项名称", "analysisItem": "分析项", "analysisItemSynonyms": "分析项同义词", "sampleCollectionNumber": "采集样品号", "documentId": "文档ID", "creationTime": "添加时间", "creator": "添加人", "remarks": "备注", "sampleNumber": "样品号", "equipmentId": "设备ID", "absorbance": "吸光度", "blank": "空白", "dwellTime": "停留时间", "correctedConcentration": "校正浓度", "peakArea": "峰面积", "peakAreaPercentage": "峰面积%", "symmetryFactor": "对称因子", "plateCount": "塔板数", "resolution": "分离度", "signalNoise": "信号噪声", "signalToNoiseRatio": "信噪比", "retentionTimeRSD": "保留时间:RSD", "areaRSD": "面积:RSD", "USPTailing": "USP拖尾", "calculatedIonRatio": "计算离子比", "wavelength": "波长", "peakHeight": "峰高", "intercept": "截距", "slope": "斜率", "correlationCoefficient": "相关系数", "scanCount": "扫描次数", "resolutionPower": "分辨率", "absorbance2": "吸光度2", "absorbance3": "吸光度3", "diameter1": "直径1", "diameter2": "直径2", "sampleDetectionValue": "样品检测值", "SDValue": "SD值", "meanValue": "均值", "crackLength2": "破长2", "crackLength3": "破长3", "negativeControl": "阴性对照", "positiveControl": "阳性对照", "linearEquation": "线性方程", "standard1Concentration": "标准品1浓度", "standard2Concentration": "标准品2浓度", "standard3Concentration": "标准品3浓度", "standard4Concentration": "标准品4浓度", "standard5Concentration": "标准品5浓度", "standard6Concentration": "标准品6浓度", "standard1Absorbance": "标准品1吸光度", "standard2Absorbance": "标准品2吸光度", "standard3Absorbance": "标准品3吸光度", "standard4Absorbance": "标准品4吸光度", "standard5Absorbance": "标准品5吸光度", "standard6Absorbance": "标准品6吸光度", "standard1CycleNumber": "标准品1循环数", "standard2CycleNumber": "标准品2循环数", "standard3CycleNumber": "标准品3循环数", "processNegativeCycle1": "过程阴性循环数1", "processNegativeCycle2": "过程阴性循环数2", "processNegativeCycle3": "过程阴性循环数3", "sampleNegativeCycle1": "样品阴性循环数1", "sampleNegativeCycle2": "样品阴性循环数2", "sampleNegativeCycle3": "样品阴性循环数3", "standard1Diameter": "标准品1直径", "standard2Diameter": "标准品2直径", "standard3Diameter": "标准品3直径", "standard4Diameter": "标准品4直径", "standard5Diameter": "标准品5直径", "standard6Diameter": "标准品6直径", "standard7Diameter": "标准品7直径", "standard8Diameter": "标准品8直径", "standard9Diameter": "标准品9直径", "standard10Diameter": "标准品10直径", "standard11Diameter": "标准品11直径", "standard12Diameter": "标准品12直径", "standard13Diameter": "标准品13直径", "standard14Diameter": "标准品14直径", "standard15Diameter": "标准品15直径", "standard16Diameter": "标准品16直径", "standard17Diameter": "标准品17直径", "standard18Diameter": "标准品18直径", "standard19Diameter": "标准品19直径", "standard20Diameter": "标准品20直径", "TOCResult": "TOC结果", "TOCAverage": "TOC平均值", "name": "名称", "freezingPointName": "冰点名称", "number": "编号", "ICAverage": "IC平均值", "TCAverage": "TC平均值", "CPMValue": "CPM值", "CPMAverage": "CPM均值", "tailingFactor": "拖尾因子", "temperatureRise": "升高温度"}, "main-form": {"type": "类型", "title": "天平日校", "model": "型号", "manufacturer": "生产厂商", "balanceRange": "天平量程", "balancePrecision": "天平精度", "verificationScale": "检定分度值", "initiateCalibration": "发起校准", "completeCalibration": "完成校准", "query": "查询", "addAttachment": "添加附件", "viewAttachment": "查看附件", "deleteAttachment": "删除附件", "recordId": "记录ID", "eventType": "事件类型", "event": "事件", "status": "状态", "weightId": "使用砝码ID", "initiator": "发起人", "initiationTime": "发起时间", "maintainer": "维护人", "maintenanceTime": "维护时间", "initiationReason": "发起原因", "addTargetPoint": "添加目标点", "addCheckPoint": "添加校验点", "delete": "删除", "connectBalance": "连接天平", "targetPoint": "目标点", "weightValue_g": "砝码值（g）", "lowerLimit_g": "低限（g）", "upperLimit_g": "高限（g）", "conclusion": "结论", "remark": "备注", "operator": "操作人", "operationTime": "操作时间", "measuredValue_g": "称量值（g）", "fileName": "文件名称", "fileDescription": "文件描述", "weight": "砝码", "reason": "原因", "connect": "连接", "disconnect": "断开", "send": "发送", "sendData": "发送数据", "receiveData": "接收数据"}, "course-form": {"title": "课程管理", "printReport": "打印报表", "courseCode": "课程代码", "courseName": "课程名称", "provider": "提供者", "fee": "费用", "additionalDocuments": "附加的文档", "viewDocuments": "查看文档", "deleteDocuments": "删除文档", "editList": "编辑列表", "removeMethod": "移除方法", "summary": "摘要", "method": "方法", "testName": "测试名称", "position": "岗位"}, "course-schedules": {"title": "培训计划"}}