<script setup lang="ts">
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { CourseFormApi } from '#/api/equipment/course-form';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, Modal, Space, TabPane, Tabs, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';

import CourseModal from './components/course-modal.vue';
import MethodModal from './components/method-modal.vue';
import { courseColumns, methodColumns } from './course-form-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: CourseModal,
  destroyOnClose: true,
});
const [MethodFormModal, methodFormModalApi] = useVbenModal({
  connectedComponent: MethodModal,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<CourseFormApi.CourseForm> = {
  columns: courseColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  data: [
    {
      courseCode: 'ID1',
      courseName: '类型一',
      CALMECHANISM: '名称一',
      CALPARAM: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      courseCode: 'ID1',
      courseName: '类型一',
      CALMECHANISM: '名称一',
      CALPARAM: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      courseCode: 'ID1',
      courseName: '类型一',
      CALMECHANISM: '名称一',
      CALPARAM: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
  ],

  // proxyConfig: {
  //   ajax: {
  //     query: async () => {
  //       return;
  //     },
  //   },
  // },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const methodGridOptions: VxeTableGridOptions<CourseFormApi.CourseForm> = {
  columns: methodColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  data: [
    {
      EQID: 'ID1',
      EQTYPE: '类型一',
      EQNAME: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      EQID: 'ID1',
      EQTYPE: '类型一',
      EQNAME: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      EQID: 'ID1',
      EQTYPE: '类型一',
      EQNAME: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
  ],

  // proxyConfig: {
  //   ajax: {
  //     query: async () => {
  //       return;
  //     },
  //   },
  // },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<CourseFormApi.CourseForm> = {
  currentChange: async ({ row }) => {
    if (row) {
      console.warn(`当前行：${row}`);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
  tableTitle: '提供课程',
});

const [MethodGrid] = useVbenVxeGrid({
  gridOptions: methodGridOptions,
  gridEvents: {},
  tableTitle: '涵盖的方法',
});
const activeKey = ref('方法');
const tabList = [
  {
    title: '方法',
  },
];
const data = ref<string>('');
const onRefresh = () => {
  // Refresh logic here
};
const addCourse = () => {
  formModalApi.setData(null).open();
};
const deleteCourse = () => {
  Modal.confirm({
    title: '询问',
    content: '此操作将会删除当前课程，继续吗',
    cancelText: '否',
    okText: '是',
    onOk() {
      console.warn('删除');
    },
    onCancel() {},
  }); // Delete logic here
  console.warn('Delete course');
};
const printReport = () => {
  // Print report logic here
  console.warn('Print report');
};
const editList = () => {
  // Edit list logic here
  methodFormModalApi.setData(null).open();
  console.warn('Edit list');
};
const removeMethod = () => {
  // Remove method logic here
  Modal.confirm({
    title: '您确定吗？',
    content: '此操作将会删除选中的方法，继续吗？',
    cancelText: '否',
    okText: '是',
    onOk() {
      console.warn('删除');
    },
    onCancel() {},
  }); // Delete logic here
  console.warn('Remove method');
};
const hasEditStatus = (row: CourseFormApi.RowType) => {
  return gridApi.grid?.isEditByRow(row);
};
const editRowEvent = (row: CourseFormApi.RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async (row: CourseFormApi.RowType) => {
  await gridApi.grid?.clearEdit();
  console.warn(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
  }, 600);
};
const cancelRowEvent = (_row: CourseFormApi.RowType) => {
  gridApi.grid?.clearEdit();
};
const uploadDocuments = () => {
  // Upload documents logic here
  console.warn('Upload documents');
};
const viewDocuments = () => {
  // View documents logic here
  console.warn('View documents');
};
const deleteDocuments = () => {
  Modal.confirm({
    title: '询问',
    content: '是否确定要删除选定的记录?',
    cancelText: '否',
    okText: '是',
    onOk() {
      console.warn('删除');
    },
    onCancel() {},
  });
  console.warn('Delete documents');
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <MethodFormModal @success="onRefresh" />
    <div class="flex h-full flex-row">
      <Grid height="100%" class="h-full w-1/3">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            <Button type="primary" @click="addCourse">
              {{ $t('equipment.add') }}
            </Button>
            <Button type="primary" @click="deleteCourse">
              {{ $t('equipment.delete') }}
            </Button>
            <Button type="primary" @click="printReport">
              {{ $t('equipment.course-form.printReport') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('basic-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('equipment.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('basic-static-tables.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
      <div class="flex-1 bg-white">
        <div class="h-1/6 p-5">
          <div class="text-base">{{ $t('equipment.course-form.summary') }}</div>
          <Textarea
            v-model:value="data"
            :auto-size="{ minRows: 5, maxRows: 6 }"
          />
        </div>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="uploadDocuments">
            {{ $t('equipment.course-form.additionalDocuments') }}
          </Button>
          <Button type="primary" @click="viewDocuments">
            {{ $t('equipment.course-form.viewDocuments') }}
          </Button>
          <Button type="primary" @click="deleteDocuments">
            {{ $t('equipment.course-form.deleteDocuments') }}
          </Button>
        </Space>
        <Tabs v-model:active-key="activeKey" class="bg-white">
          <TabPane
            v-for="item in tabList"
            :key="item.title"
            :tab="item.title"
          />
        </Tabs>
        <MethodGrid height="auto" class="h-[78%]">
          <template #toolbar-actions>
            <Space :size="[8, 4]" wrap>
              <Button type="primary" @click="editList">
                {{ $t('equipment.course-form.editList') }}
              </Button>
              <Button type="primary" @click="removeMethod">
                {{ $t('equipment.course-form.removeMethod') }}
              </Button>
            </Space>
          </template>
        </MethodGrid>
      </div>
    </div>
  </Page>
</template>
