# 表单验证功能使用说明

## 概述

在 `prompt-for-product-step.vue` 中实现了根据不同Key调用对应表单validate方法的功能。该功能支持：

1. 根据指定Key验证特定表单
2. 验证当前激活的表单
3. 验证所有表单
4. 完善的错误处理机制

## 表单Key映射关系

```typescript
const FORM_KEY_MAP = {
  '1': 'rawMaterial',    // 原辅包表单
  '2': 'adHoc',          // 常规样表单  
  '3': 'inspection',     // 免检表单
} as const;
```

## 核心方法

### 验证方法

#### 1. validateFormByKey(key: string)

根据指定Key验证对应的表单。

**参数：**
- `key`: 表单标识符 ('1', '2', '3')

**返回值：**
```typescript
Promise<{
  success: boolean;
  data?: any;                // 验证成功时的数据
  error?: string;            // 验证失败时的错误信息
  validationResult?: any;    // 完整的验证结果对象（包含errors、values等）
}>
```

**使用示例：**
```typescript
// 验证原辅包表单
const result = await validateFormByKey('1');
if (result.success) {
  console.log('验证成功:', result.data);
} else {
  console.error('验证失败:', result.error);
}
```

#### 2. validateCurrentForm()

验证当前激活的表单（基于activeKey）。

**返回值：** 同 `validateFormByKey`

**使用示例：**
```typescript
const result = await validateCurrentForm();
```

#### 3. validateAllForms()

验证所有表单。

**返回值：**
```typescript
Promise<{
  success: boolean;  // 所有表单都验证成功时为true
  results: Array<{
    key: string;
    success: boolean;
    data?: any;
    error?: string;
  }>;
}>
```

**使用示例：**
```typescript
const result = await validateAllForms();
console.log('整体结果:', result.success);
result.results.forEach(item => {
  console.log(`表单 ${item.key}:`, item.success ? '成功' : item.error);
});
```

### 提交方法

#### 4. submitFormByKey(key: string)

根据指定Key提交对应的表单。

**参数：**
- `key`: 表单标识符 ('1', '2', '3')

**返回值：**
```typescript
Promise<{
  success: boolean;
  data?: any;        // 提交成功时的表单数据
  error?: string;    // 提交失败时的错误信息
}>
```

**使用示例：**
```typescript
// 提交原辅包表单
const result = await submitFormByKey('1');
if (result.success) {
  console.log('提交成功:', result.data);
} else {
  console.error('提交失败:', result.error);
}
```

#### 5. submitCurrentForm()

提交当前激活的表单（基于activeKey）。

**返回值：** 同 `submitFormByKey`

**使用示例：**
```typescript
const result = await submitCurrentForm();
```

## 错误处理

系统提供了完善的错误处理机制：

1. **无效Key检查**：验证传入的Key是否在有效范围内
2. **表单实例检查**：确保表单组件已正确加载
3. **API实例检查**：确保表单API可用
4. **验证结果检查**：正确解析 `formApi.validate()` 返回的 `valid` 字段
5. **字段级错误处理**：提取并格式化具体的字段验证错误信息
6. **异常捕获**：捕获验证过程中的所有异常
7. **用户友好提示**：在Modal中显示清晰的错误信息，并阻止错误时的关闭行为

### 验证失败时的行为

- 显示具体的字段错误信息
- 使用 `message.error()` 显示用户友好的提示
- 阻止Modal关闭，让用户有机会修正数据
- 在控制台输出详细的调试信息

## 在父组件中使用

```vue
<template>
  <div>
    <Button @click="handleValidate">验证表单</Button>
    <Button @click="handleSubmit">提交表单</Button>
    <PromptForProductStep ref="formStepRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import PromptForProductStep from './prompt-for-product-step.vue';

const formStepRef = ref();

const handleValidate = async () => {
  // 验证指定表单
  const result1 = await formStepRef.value.validateFormByKey('1');

  // 验证当前表单
  const result2 = await formStepRef.value.validateCurrentForm();

  // 验证所有表单
  const result3 = await formStepRef.value.validateAllForms();
};

const handleSubmit = async () => {
  // 提交指定表单
  const result1 = await formStepRef.value.submitFormByKey('1');

  // 提交当前表单
  const result2 = await formStepRef.value.submitCurrentForm();
};
</script>
```

## Modal onConfirm 流程

Modal的确认按钮现在执行以下流程：

1. **防重复提交检查**：检查 `isSubmitting` 状态
2. **表单验证**：调用 `validateCurrentForm()` 验证当前激活表单
3. **验证失败处理**：显示错误信息，不关闭Modal
4. **表单提交**：验证成功后调用 `submitCurrentForm()` 提交表单
5. **提交成功处理**：显示成功信息，关闭Modal
6. **提交失败处理**：显示错误信息，不关闭Modal
7. **状态重置**：无论成功失败都重置 `isSubmitting` 状态

## 注意事项

1. **组件加载**：确保在调用验证方法前，表单组件已完全加载
2. **异步处理**：所有验证方法都是异步的，需要使用 await 或 .then()
3. **错误处理**：建议在调用时添加 try-catch 块处理异常
4. **表单状态**：验证不会改变表单的数据，只检查当前数据的有效性

## 扩展说明

如果需要添加新的表单类型：

1. 在 `FORM_KEY_MAP` 中添加新的映射关系
2. 在 `formInstanceMap` 中添加对应的ref
3. 在 `getFormApi` 方法中添加对应的API获取逻辑
4. 在模板中为新表单组件添加ref引用
5. 确保新表单组件通过 `defineExpose` 暴露了表单API
