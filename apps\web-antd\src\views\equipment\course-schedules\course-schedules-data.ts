import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CourseSchedulesFormApi } from '#/api/equipment/course-schedules';

import { $t } from '#/locales';

export function courseColumns(): VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'courseCode',
      title: $t('equipment.course-form.courseCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'courseName',
      title: $t('equipment.course-form.courseName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'provider',
      title: $t('equipment.course-form.provider'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'fee',
      title: $t('equipment.course-form.fee'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 180,
    },
  ];
}

export function methodColumns(): VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'courseCode',
      title: $t('equipment.course-form.method'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'courseName',
      title: $t('equipment.course-form.testName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'provider',
      title: $t('equipment.course-form.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function courseSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'courseCode',
      rules: 'required',
      label: $t('equipment.course-form.courseCode'),
    },
    {
      component: 'Input',
      fieldName: 'courseName',
      label: $t('equipment.course-form.courseName'),
      rules: 'required',
    },

    {
      component: 'Input',
      fieldName: 'CALMECHANISM',
      label: $t('equipment.course-form.provider'),
    },
    {
      component: 'Input',
      fieldName: 'CALPARAM',
      label: $t('equipment.course-form.fee'),
    },
  ];
}
